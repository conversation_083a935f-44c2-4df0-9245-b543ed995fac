<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, onUnmounted, computed } from 'vue';

// --- CLOCK & DATE ---
const currentTime = ref('');
const currentDate = ref('');
let clockInterval = null;

const updateTime = () => {
    const now = new Date();
    currentTime.value = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true });
    currentDate.value = now.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
};

// --- CALENDAR ---
const date = ref(new Date());
const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

const currentMonth = computed(() => monthNames[date.value.getMonth()]);
const currentYear = computed(() => date.value.getFullYear());

const daysInMonth = computed(() => {
    const year = date.value.getFullYear();
    const month = date.value.getMonth();
    const firstDay = new Date(year, month, 1).getDay();
    const lastDate = new Date(year, month + 1, 0).getDate();
    
    let days = [];
    // Add blank days for the first week
    for (let i = 0; i < firstDay; i++) {
        days.push({ day: '', isToday: false, isCurrentMonth: false });
    }
    // Add actual days
    for (let i = 1; i <= lastDate; i++) {
        const today = new Date();
        const isToday = i === today.getDate() && month === today.getMonth() && year === today.getFullYear();
        days.push({ day: i, isToday, isCurrentMonth: true });
    }
    return days;
});

const prevMonth = () => {
    date.value = new Date(date.value.setMonth(date.value.getMonth() - 1));
};

const nextMonth = () => {
    date.value = new Date(date.value.setMonth(date.value.getMonth() + 1));
};

// --- API USAGE DATA ---
const usageStats = [
  { name: 'API Requests', value: '0' },
  { name: 'Forward Geocoding', value: '0' },
  { name: 'Reverse Geocoding', value: '0' },
  { name: 'Errors', value: '0' },
];

onMounted(() => {
    updateTime();
    clockInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
    clearInterval(clockInterval);
});
</script>

<template>
    <AppLayout title="Dashboard">
        <div class="">
            <div class="py-12">
                <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Left Column -->
                        <div class="lg:col-span-2 space-y-8">
                            <!-- Welcome Header -->
                            <div class="px-4 sm:px-0">
                                <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
                                <p class="mt-2 text-md text-gray-600">API usage summary and account status.</p>
                            </div>

                            <!-- API Usage Cards -->
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                                <div v-for="item in usageStats" :key="item.name" class="bg-white border border-gray-200 rounded-2xl p-6 hover:border-gray-400 transition-colors duration-300">
                                    <p class="text-sm font-medium text-gray-500">{{ item.name }}</p>
                                    <p class="mt-2 text-4xl font-bold text-gray-900">{{ item.value }}</p>
                                </div>
                            </div>

                            <!-- Subscription Section -->
                            <div class="bg-white border border-gray-200 rounded-2xl">
                                <div class="p-6 sm:p-8">
                                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                        <div>
                                            <h3 class="text-lg leading-6 font-bold text-gray-900">Subscription Plan</h3>
                                            <p class="mt-2 max-w-2xl text-sm text-gray-600">
                                                You are currently on the <span class="font-semibold text-gray-900">Free Plan</span>.
                                            </p>
                                        </div>
                                        <div class="mt-5 md:mt-0 md:ml-4 flex-shrink-0">
                                            <button type="button" class="w-full md:w-auto inline-flex items-center justify-center px-5 py-2.5 border border-gray-300 text-sm font-medium rounded-lg shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors">
                                                Upgrade Plan
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- empty table card showing user last api geo search request liest-->
                             <div class="bg-white border border-gray-200 rounded-2xl p-6">
                                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                    <div>
                                        <h3 class="text-lg leading-6 font-bold text-gray-900">Last API Geo Search Requests</h3>
                                        
                                    </div>
                                </div>
                                <p class="mt-2 text-sm text-gray-600 py-32 text-center ">
                                            No recent requests found.
                                </p>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-8">
                            <!-- Clock and Location -->
                            <div class="bg-white border border-gray-200 rounded-2xl p-6 text-center">
                                <p class="text-5xl font-mono font-bold text-gray-900">{{ currentTime }}</p>
                                <p class="mt-2 text-md text-gray-600">{{ currentDate }}</p>
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <p class="text-sm text-gray-500">Current Location</p>
                                    <p class="text-lg font-bold text-gray-900">Kigali, Rwanda</p>
                                </div>
                            </div>
                            
                            <!-- Calendar -->
                            <div class="bg-white border border-gray-200 rounded-2xl p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-bold text-gray-900">{{ currentMonth }} {{ currentYear }}</h3>
                                    <div class="flex space-x-2">
                                        <button @click="prevMonth" class="p-2 rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 hover:text-gray-700 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                                        </button>
                                        <button @click="nextMonth" class="p-2 rounded-full bg-gray-100 text-gray-500 hover:bg-gray-200 hover:text-gray-700 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l7-7-7-7" /></svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="grid grid-cols-7 gap-2 text-center text-sm">
                                    <div v-for="day in ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']" :key="day" class="text-gray-500 font-medium">{{ day }}</div>
                                    <div v-for="(d, index) in daysInMonth" :key="index" 
                                         :class="['p-2 rounded-full', { 'text-gray-900': d.isCurrentMonth, 'text-gray-400': !d.isCurrentMonth, 'bg-gray-900 text-white font-bold': d.isToday }]">
                                        {{ d.day }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Using Tailwind classes for styling, so minimal custom CSS is needed */
</style>