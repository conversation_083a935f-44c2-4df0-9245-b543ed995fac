<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { Link } from '@inertiajs/vue3';
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';
import { Edit, Trash2, Plus, Eye, EyeOff } from 'lucide-vue-next';

const props = defineProps({
    placeMapId: { type: String, required: true},
    dataMapId: { type: String, required: true },
    dataMapItemId: { type: String, required: true }
});

const dataMap = ref(null);
const dataMapItem = ref(null);
const customDataItems = ref([]);
const customDataPagination = ref(null);
const itemsPerPage = ref(10);
const itemsPerPageOptions = ref([5, 10, 25, 50]);
const alert = ref({ show: false, type: '', message: '' });
const showCreateEditModal = ref(false);
const isEditingData = ref(false);
const editingDataId = ref(null);
const showAllFields = ref(false);

const customDataForm = ref({
    dataMapID: props.dataMapId,
    dataMapItemID: props.dataMapItemId,
    objectID: '',
    dataItems: [],
    processing: false,
    errors: {}
});

// Get custom fields configuration from dataMap
const customFields = computed(() => {
    if (!dataMap.value?.customFields) return [];

    // Handle both array and string formats
    let fields = dataMap.value.customFields;
    if (typeof fields === 'string') {
        try {
            fields = JSON.parse(fields);
        } catch (e) {
            console.error('Error parsing customFields:', e);
            return [];
        }
    }

    // Ensure it's an array
    return Array.isArray(fields) ? fields : [];
});

// Get first 4 fields for display
const displayFields = computed(() => {
    const fields = customFields.value || [];
    return fields.slice(0, 4);
});

// Get hidden fields (beyond first 4)
const hiddenFields = computed(() => {
    const fields = customFields.value || [];
    return fields.slice(4);
});

// Get all fields or just display fields based on showAllFields
const visibleFields = computed(() => {
    return showAllFields.value ? customFields.value : displayFields.value;
});

const showAlert = (type, message, duration = 4000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => alert.value.show = false, duration);
};

// Helper function to get field configuration by name
const getFieldConfig = (fieldName) => {
    const fields = customFields.value || [];
    return fields.find(field => field.name === fieldName);
};

const fetchDataMapDetails = async () => {
    try {
        // Use the API endpoint to get dataMap details
        const response = await axios.get(`/map/data/${props.placeMapId}/${props.dataMapId}`);
        dataMap.value = response.data;
        console.log('Fetched DataMap:', response.data);
    } catch (error) {
        console.error('Error fetching data map details:', error);
        showAlert('error', 'Error fetching data map details.');
    }
};

const fetchDataMapItem = async () => {
    try {
        // Use the API endpoint to get dataMapItems and find the specific one
        const response = await axios.get(`/map/data/items/${props.dataMapId}`);
        const items = response.data.data;
        dataMapItem.value = items.find(item => item.id == props.dataMapItemId);
        console.log('Fetched DataMapItem:', dataMapItem.value);
    } catch (error) {
        console.error('Error fetching data map item details:', error);
        showAlert('error', 'Error fetching data map item details.');
    }
};

const fetchCustomData = async (page = 1, perPage = itemsPerPage.value) => {
    try {
        const params = {
            dataMapID: props.dataMapId,
            dataMapItemID: props.dataMapItemId,
            page,
            perPage
        };
        console.log('Fetching custom data with params:', params);

        const response = await axios.get('/map/data/get-custom-data', { params });
        console.log('Custom data response:', response.data);

        customDataItems.value = response.data.data;
        customDataPagination.value = {
            currentPage: response.data.currentPage,
            lastPage: response.data.lastPage,
            total: response.data.total,
            perPage: response.data.perPage,
        };
    } catch (error) {
        console.error('Error fetching custom data:', error);
        showAlert('error', 'Error fetching custom data.');
    }
};

onMounted(async () => {
    console.log('Props:', props);
    console.log('API Endpoints:');
    console.log('- DataMap API:', `/map/data/${props.placeMapId || '1'}/${props.dataMapId}`);
    console.log('- DataMapItems API:', `/map/data/items/${props.dataMapId}`);
    console.log('- CustomData API:', '/map/data/get-custom-data');

    await fetchDataMapDetails();
    await fetchDataMapItem();
    await fetchCustomData();

    // Debug: Log the final data
    console.log('Final DataMap:', dataMap.value);
    console.log('Final DataMapItem:', dataMapItem.value);
    console.log('Final Custom Fields:', customFields.value);
});

const openCreateModal = () => {
    isEditingData.value = false;
    editingDataId.value = null;

    // Initialize form with custom fields from dataMap
    const fields = customFields.value || [];
    customDataForm.value = {
        dataMapID: props.dataMapId,
        dataMapItemID: props.dataMapItemId,
        objectID: '',
        dataItems: fields.map(field => ({
            name: field.name,
            value: field.default && field.default !== 'no' ? field.default : ''
        })),
        processing: false,
        errors: {}
    };
    showCreateEditModal.value = true;
};

const openEditModal = (dataItem) => {
    isEditingData.value = true;
    editingDataId.value = dataItem.id;

    // Initialize form with existing data from the selected item
    const fields = customFields.value || [];
    customDataForm.value = {
        dataMapID: props.dataMapId,
        dataMapItemID: props.dataMapItemId,
        objectID: dataItem.id,
        dataItems: fields.map(field => ({
            name: field.name,
            value: dataItem[field.name] || ''
        })),
        processing: false,
        errors: {}
    };
    showCreateEditModal.value = true;
};

const submitCustomData = async () => {
    customDataForm.value.processing = true;
    customDataForm.value.errors = {};

    const url = isEditingData.value
        ? route('dataMap.updateCustomData')
        : route('dataMap.createCustomData');

    try {
        const response = await axios[isEditingData.value ? 'put' : 'post'](url, customDataForm.value);
        showAlert('success', response.data.message);
        await fetchCustomData(customDataPagination.value?.currentPage || 1, itemsPerPage.value);
        showCreateEditModal.value = false;
    } catch (error) {
        showAlert('error', error.response?.data?.message || 'An error occurred.');
        if (error.response?.status === 422) {
            customDataForm.value.errors = error.response.data.errors;
        }
    } finally {
        customDataForm.value.processing = false;
    }
};

const deleteCustomData = async (dataItem) => {
    if (!confirm('Are you sure you want to delete this data item? This action cannot be undone.')) {
        return;
    }

    try {
        const response = await axios.delete(route('dataMap.removeCustomData'), {
            data: {
                dataMapID: props.dataMapId,
                dataMapItemID: props.dataMapItemId,
                objectID: dataItem.id
            }
        });
        showAlert('success', response.data.message);
        await fetchCustomData(customDataPagination.value?.currentPage || 1, itemsPerPage.value);
    } catch (error) {
        showAlert('error', error.response?.data?.message || 'An error occurred while deleting the item.');
    }
};
</script>

<template>
    <AppLayout :title="dataMapItem ? `Custom Data for ${dataMapItem.name}` : 'Custom Data'">
        <!-- Alert -->
        <div v-if="alert.show" class="fixed bottom-6 left-6 z-50 w-full max-w-sm rounded-lg border p-4 shadow-lg flex items-start"
             :class="alert.type === 'success' ? 'border-green-300 bg-green-50 text-green-800' : 'border-red-300 bg-red-50 text-red-800'">
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">{{ alert.message }}</p>
            </div>
            <button @click="alert.show = false" class="ml-auto bg-transparent rounded-md p-1.5 text-gray-500 hover:text-gray-800">
                <span class="sr-only">Dismiss</span>
                <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
            </button>
        </div>

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div v-if="dataMapItem" class="bg-white overflow-hidden shadow-xl border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <div class="mb-6">
                        <Link :href="route('myMapData.show', { placeMapId: props.placeMapId || '1', dataMapId: props.dataMapId })"
                              class="text-sm text-indigo-600 hover:underline mb-4 inline-block">&larr; Back to Data Items</Link>
                        <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Custom Data for {{ dataMapItem.name }}</h1>
                                <p class="mt-1 text-md text-gray-600">{{ dataMapItem.description }}</p>
                            </div>
                            <div class="flex space-x-2">
                                <button @click="openCreateModal"
                                        class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-black text-white hover:bg-gray-800 px-4 py-2">
                                    <Plus class="w-4 h-4 mr-2" />
                                    Add Custom Data
                                </button>
                                <button v-if="hiddenFields.length > 0" @click="showAllFields = !showAllFields"
                                        class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 px-4 py-2">
                                    <Eye v-if="!showAllFields" class="w-4 h-4 mr-2" />
                                    <EyeOff v-else class="w-4 h-4 mr-2" />
                                    {{ showAllFields ? 'Show Less' : 'Show All Fields' }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Data Table -->
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 bg-white">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th v-for="field in visibleFields" :key="field.name" scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        {{ field.name }}
                                        <span v-if="field.isRequired === 'yes'" class="text-red-500 ml-1">*</span>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr v-for="item in customDataItems" :key="item.id" class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-600">{{ item.id }}</td>
                                    <td v-for="field in visibleFields" :key="field.name"
                                        class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ item[field.name] || '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ item.timestamp ? new Date(item.timestamp).toLocaleString() : '-' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <button @click="openEditModal(item)"
                                                    class="inline-flex items-center text-indigo-600 hover:text-indigo-800 hover:underline">
                                                <Edit class="w-4 h-4 mr-1" />
                                                Edit
                                            </button>
                                            <button @click="deleteCustomData(item)"
                                                    class="inline-flex items-center text-red-600 hover:text-red-800 hover:underline">
                                                <Trash2 class="w-4 h-4 mr-1" />
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr v-if="customDataItems.length === 0">
                                    <td :colspan="visibleFields.length + 3" class="text-center text-gray-500 py-12">
                                        <p class="text-lg">No custom data found for this item.</p>
                                        <p class="text-sm">Get started by adding custom data.</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div v-if="customDataPagination && customDataPagination.total > 0" class="mt-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>Per Page:</span>
                            <select id="itemsPerPage" v-model="itemsPerPage" @change="fetchCustomData(1, $event.target.value)"
                                    class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-1">
                                <option v-for="option in itemsPerPageOptions" :key="option" :value="option">{{ option }}</option>
                            </select>
                            <span class="hidden sm:inline">| Total Items: {{ customDataPagination.total }}</span>
                        </div>
                        <nav v-if="customDataPagination.lastPage > 1" class="flex items-center justify-center">
                            <button @click="fetchCustomData(customDataPagination.currentPage - 1, itemsPerPage)"
                                    :disabled="customDataPagination.currentPage <= 1"
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                &laquo; Prev
                            </button>
                            <span class="px-4 py-1 text-sm text-gray-800 bg-gray-100 border-t border-b border-gray-300">
                                Page {{ customDataPagination.currentPage }} of {{ customDataPagination.lastPage }}
                            </span>
                            <button @click="fetchCustomData(customDataPagination.currentPage + 1, itemsPerPage)"
                                    :disabled="customDataPagination.currentPage >= customDataPagination.lastPage"
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                Next &raquo;
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Loading state -->
                <div v-else class="bg-white overflow-hidden shadow-xl border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <div class="mb-6">
                        <div class="animate-pulse bg-gray-200 rounded-md h-4 w-1/4 mb-4"></div>
                        <div class="flex flex-col md:flex-row justify-between md:items-center gap-4">
                            <div>
                                <div class="animate-pulse bg-gray-200 rounded-md h-8 w-48 mb-2"></div>
                                <div class="animate-pulse bg-gray-200 rounded-md h-4 w-64"></div>
                            </div>
                            <div class="animate-pulse bg-gray-200 rounded-md h-10 w-32"></div>
                        </div>
                    </div>
                    <div class="divide-y divide-gray-200">
                        <div class="py-4" v-for="i in 5" :key="i">
                            <div class="flex items-center justify-between">
                                <div class="flex-1 space-y-2">
                                    <div class="animate-pulse bg-gray-200 rounded-md h-4 w-1/3"></div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="animate-pulse bg-gray-200 rounded-md h-8 w-16"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <div v-if="showCreateEditModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg shadow-2xl p-8 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <h2 class="text-2xl font-bold mb-6">{{ isEditingData ? 'Edit' : 'Create' }} Custom Data</h2>
                <form @submit.prevent="submitCustomData">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div v-for="(dataItem, index) in customDataForm.dataItems" :key="index" class="space-y-2">
                            <label class="text-sm font-medium block" :for="`field-${index}`">
                                {{ dataItem.name }}
                                <span v-if="getFieldConfig(dataItem.name)?.isRequired === 'yes'" class="text-red-500">*</span>
                            </label>

                            <!-- Text input -->
                            <input v-if="!getFieldConfig(dataItem.name)?.type || getFieldConfig(dataItem.name)?.type === 'text' || getFieldConfig(dataItem.name)?.type === 'string'"
                                   :id="`field-${index}`"
                                   v-model="dataItem.value"
                                   type="text"
                                   :placeholder="`Enter ${dataItem.name}`"
                                   :maxlength="getFieldConfig(dataItem.name)?.length || 255"
                                   :required="getFieldConfig(dataItem.name)?.isRequired === 'yes'"
                                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">

                            <!-- Number input -->
                            <input v-else-if="getFieldConfig(dataItem.name)?.type === 'number'"
                                   :id="`field-${index}`"
                                   v-model="dataItem.value"
                                   type="number"
                                   :placeholder="`Enter ${dataItem.name}`"
                                   :required="getFieldConfig(dataItem.name)?.isRequired === 'yes'"
                                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">

                            <!-- Date input -->
                            <input v-else-if="getFieldConfig(dataItem.name)?.type === 'date'"
                                   :id="`field-${index}`"
                                   v-model="dataItem.value"
                                   type="date"
                                   :required="getFieldConfig(dataItem.name)?.isRequired === 'yes'"
                                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">

                            <!-- Decimal input -->
                            <input v-else-if="getFieldConfig(dataItem.name)?.type === 'decimal'"
                                   :id="`field-${index}`"
                                   v-model="dataItem.value"
                                   type="number"
                                   step="0.01"
                                   :placeholder="`Enter ${dataItem.name}`"
                                   :required="getFieldConfig(dataItem.name)?.isRequired === 'yes'"
                                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">

                            <!-- Default text input for other types -->
                            <input v-else
                                   :id="`field-${index}`"
                                   v-model="dataItem.value"
                                   type="text"
                                   :placeholder="`Enter ${dataItem.name}`"
                                   :maxlength="getFieldConfig(dataItem.name)?.length || 255"
                                   :required="getFieldConfig(dataItem.name)?.isRequired === 'yes'"
                                   class="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">

                            <div v-if="customDataForm.errors[`dataItems.${index}.value`]" class="text-red-500 text-sm">
                                {{ customDataForm.errors[`dataItems.${index}.value`][0] }}
                            </div>

                            <!-- Field info -->
                            <p v-if="getFieldConfig(dataItem.name)" class="text-xs text-gray-500">
                                Type: {{ getFieldConfig(dataItem.name).type || 'text' }}
                                <span v-if="getFieldConfig(dataItem.name).length"> | Max length: {{ getFieldConfig(dataItem.name).length }}</span>
                            </p>
                        </div>
                    </div>

                    <!-- Form errors -->
                    <div v-if="customDataForm.errors.dataMapID || customDataForm.errors.dataMapItemID || customDataForm.errors.dataItems"
                         class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                        <div v-if="customDataForm.errors.dataMapID" class="text-red-600 text-sm">{{ customDataForm.errors.dataMapID[0] }}</div>
                        <div v-if="customDataForm.errors.dataMapItemID" class="text-red-600 text-sm">{{ customDataForm.errors.dataMapItemID[0] }}</div>
                        <div v-if="customDataForm.errors.dataItems" class="text-red-600 text-sm">{{ customDataForm.errors.dataItems[0] }}</div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-4">
                        <button type="button" @click="showCreateEditModal = false"
                                class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-gray-200 text-gray-800 hover:bg-gray-300 px-4 py-2">
                            Cancel
                        </button>
                        <button type="submit" :disabled="customDataForm.processing"
                                class="inline-flex items-center justify-center rounded-md text-sm font-medium bg-black text-white hover:bg-gray-800 px-4 py-2 disabled:opacity-50">
                            {{ customDataForm.processing ? 'Saving...' : (isEditingData ? 'Update' : 'Create') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
