<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { <PERSON>, <PERSON> } from '@inertiajs/vue3';
import { ref, onMounted, computed, defineComponent, h } from 'vue';
import axios from 'axios';
import { <PERSON>, LockO<PERSON>, CircleCheck, CircleX } from 'lucide-vue-next';
import { Switch } from "@/components/ui/switch";

const MapIcon = defineComponent({
    props: ['name', 'size'],
    setup(props) {
        const size = props.size || 20;
        
        const iconPaths = {
            pin: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/><circle cx="12" cy="9" r="2.5"/>`,
            star: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>`,
            heart: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>`,
            flag: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"/><line x1="4" y1="22" x2="4" y2="15"/>`,
            home: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9,22 9,12 15,12 15,22"/>`,
            work: `<rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/><line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/><line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>`,
            cafe: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z" stroke="currentColor" stroke-width="2" fill="none"/>`,
            park: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 22v-6m0 0l-3-3m3 3l3-3m-3-10v10m0-10l-3-3m3 3l3-3M12 2v10"/><path d="M5 12h14" stroke="currentColor" stroke-width="2"/>`,
            restaurant: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 2v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V2"/><path d="M7 2v20"/><path d="M21 15v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7"/>`,
            shopping: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/><line x1="3" y1="6" x2="21" y2="6"/><path d="M16 10a4 4 0 0 1-8 0"/>`,
            hospital: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v12m6-6H6"/><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>`,
            school: `<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c3 3 9 3 12 0v-5"/>`,
        };

        return () => h('svg', {
            width: size,
            height: size,
            viewBox: '0 0 24 24',
            fill: 'none',
            innerHTML: iconPaths[props.name] || iconPaths.pin
        });
    }
});

// Reusable Alert Component
const Alert = defineComponent({
    props: ['type', 'message'],
    emits: ['close'],
    setup(props, { emit }) {
        const alertClasses = computed(() => {
            const base = 'fixed bottom-6 left-6 z-[100] max-w-sm p-4 rounded-lg border-2 shadow-lg flex justify-between items-center';
            if (props.type === 'success') return `${base} bg-green-50 text-green-900 border-green-400`;
            if (props.type === 'error') return `${base} bg-red-50 text-red-900 border-red-400`;
            return `${base} bg-blue-50 text-blue-900 border-blue-400`;
        });

        return () => h('div', { class: alertClasses.value, role: 'alert' }, [
            h('span', { class: 'font-medium text-sm' }, props.message),
            h('button', {
                type: 'button',
                class: 'ml-4 -mt-1 font-bold text-2xl hover:text-gray-700',
                onClick: () => emit('close')
            }, '×')
        ]);
    }
});

// Shadcn-like components
const Button = 'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';
const ButtonPrimary = `${Button} bg-black text-white hover:bg-gray-800 shadow-md`;
const ButtonSecondary = `${Button} bg-gray-200 text-gray-800 hover:bg-gray-300`;
const ButtonDestructive = `${Button} bg-red-600 text-white hover:bg-red-700`;

const Input = 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50';
const Label = 'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70';
const Select = `${Input}`;

// Props
const props = defineProps({
    placeMapId: {
        type: Number,
        required: true,
    }
});

// State
const placeMap = ref(null);
const dataMaps = ref([]);
const pagination = ref({});
const searchQuery = ref('');
const perPage = ref(10);
const perPageOptions = ref([5, 10, 25, 50]);
const loading = ref(false);
const alert = ref({ show: false, type: '', message: '' });

const showCreateEditModal = ref(false);
const isEditing = ref(false);
const editingDataMapId = ref(null);
const modalTab = ref('main'); // 'main' or 'customFields'

const getInitialForm = () => ({
    name: '',
    image: 'pin',
    description: '',
    visibility: 'private',
    status: 'active',
    customFields: [],
    processing: false,
    errors: {}
});

const form = ref(getInitialForm());

const mapIcons = ref([
    { name: 'pin', label: 'Location Pin' },
    { name: 'star', label: 'Star' },
    { name: 'heart', label: 'Heart' },
    { name: 'flag', label: 'Flag' },
    { name: 'home', label: 'Home' },
    { name: 'work', label: 'Work' },
    { name: 'cafe', label: 'Cafe' },
    { name: 'park', label: 'Park' },
    { name: 'restaurant', label: 'Restaurant' },
    { name: 'shopping', label: 'Shopping' },
    { name: 'hospital', label: 'Hospital' },
    { name: 'school', label: 'School' }
]);

// Functions
const showAlert = (type, message, duration = 3000) => {
    alert.value = { show: true, type, message };
    setTimeout(() => alert.value.show = false, duration);
};

const fetchPlaceMapDetails = async () => {
    try {
        const response = await axios.get(route('placeMap.getById', { placeMapId: props.placeMapId }));
        placeMap.value = response.data;
    } catch (error) {
        showAlert('error', 'Error fetching place map details.');
        console.error('Error fetching place map details:', error);
    }
};

const fetchDataMaps = async (page = 1, perPageValue = perPage.value) => {
    loading.value = true;
    try {
        const response = await axios.get(route('dataMap.index', {
            placeMapId: props.placeMapId,
            page,
            search: searchQuery.value,
            perPage: perPageValue
        }));
        dataMaps.value = response.data.data;
        pagination.value = {
            currentPage: response.data.currentPage,
            lastPage: response.data.lastPage,
            total: response.data.total,
            perPage: response.data.perPage,
        };
        perPage.value = perPageValue;
    } catch (error) {
        showAlert('error', 'Error fetching data maps.');
        console.error('Error fetching data maps:', error);
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchPlaceMapDetails();
    fetchDataMaps();
});

const openCreateModal = () => {
    isEditing.value = false;
    editingDataMapId.value = null;
    form.value = getInitialForm();
    modalTab.value = 'main';
    showCreateEditModal.value = true;
};

const openEditModal = (dataMap) => {
    isEditing.value = true;
    editingDataMapId.value = dataMap.id;
    
    const customFields = dataMap.customFields ? (typeof dataMap.customFields === 'string' ? JSON.parse(dataMap.customFields) : JSON.parse(JSON.stringify(dataMap.customFields))) : [];

    customFields.forEach(field => {
        if (field.type === 'subItems' && !Array.isArray(field.subFields)) {
            field.subFields = [];
        }
    });

    form.value = {
        name: dataMap.name,
        image: dataMap.image,
        description: dataMap.description,
        visibility: dataMap.visibility,
        status: dataMap.status,
        customFields: customFields,
        processing: false,
        errors: {}
    };
    modalTab.value = 'main';
    showCreateEditModal.value = true;
};

const addCustomField = () => {
    form.value.customFields.push({ name: '', type: 'text', length: 255, isRequired: 'no', default: 'no', subFields: [] });
};

const removeCustomField = (index) => {
    form.value.customFields.splice(index, 1);
};

const addSubField = (parentIndex) => {
    const parentField = form.value.customFields[parentIndex];
    if (!parentField.subFields) {
        parentField.subFields = [];
    }
    parentField.subFields.push({ name: '', type: 'text', isRequired: 'no' });
};

const removeSubField = (parentIndex, subIndex) => {
    form.value.customFields[parentIndex].subFields.splice(subIndex, 1);
};

const submitDataMap = async () => {
    form.value.processing = true;
    form.value.errors = {};
    
    const url = isEditing.value
        ? route('dataMap.update', { placeMapId: props.placeMapId, dataMapId: editingDataMapId.value })
        : route('dataMap.store', { placeMapId: props.placeMapId });
    
    const method = isEditing.value ? 'put' : 'post';

    try {
        const response = await axios[method](url, form.value);
        showAlert('success', response.data.message);
        showCreateEditModal.value = false;
        fetchDataMaps(pagination.value.currentPage, perPage.value);
    } catch (error) {
        const errorMessage = error.response?.data?.message || 'An unexpected error occurred.';
        showAlert('error', errorMessage);
        if (error.response && error.response.status === 422 && error.response.data.errors) {
            form.value.errors = error.response.data.errors;
        } else {
            console.error('An unexpected error occurred:', error);
        }
    } finally {
        form.value.processing = false;
    }
};
</script>

<template>
    <AppLayout :title="placeMap ? `Data Maps for ${placeMap.name}` : 'Data Maps'">
        <Alert v-if="alert.show" :type="alert.type" :message="alert.message" @close="alert.show = false" />
        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white overflow-hidden shadow-xl border border-gray-200 sm:rounded-lg p-6 md:p-8">
                    <div v-if="placeMap" class="flex flex-col md:flex-row justify-between md:items-center mb-6 gap-4">
                        <div>
                            <Link :href="route('myMap.show', { placeMapId: placeMapId })" class="text-sm text-indigo-600 hover:underline mb-2 inline-block">&larr; Back to {{ placeMap.name }} Items</Link>
                            <h1 class="text-3xl font-bold text-gray-900">Data for {{ placeMap.name }}</h1>
                        </div>
                        <button @click="openCreateModal" :class="`${ButtonPrimary} px-4 py-2`">Create New Data</button>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template v-if="dataMaps.length > 0">
                                    <tr v-for="dataMap in dataMaps" :key="dataMap.id" class="hover:bg-gray-50 transition-colors duration-200">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                                    <MapIcon :name="dataMap.image || 'pin'" :size="24" class="text-gray-700" />
                                                </div>
                                                <div class="ml-4">
                                                    {{ dataMap.name }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">{{ dataMap.description }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span :class="['px-2 inline-flex text-xs leading-5 font-semibold rounded-full', dataMap.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800']">
                                                {{ dataMap.status }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-4">
                                            <Link :href="route('myMapData.show', { placeMapId: placeMapId, dataMapId: dataMap.id })" class="font-medium text-indigo-600 hover:text-indigo-800 hover:underline">
                                                Manage Items
                                            </Link>
                                            <button @click="openEditModal(dataMap)" class="font-medium text-gray-600 hover:text-gray-800 hover:underline">Edit</button>
                                        </td>
                                    </tr>
                                </template>
                                <tr v-else-if="!loading">
                                    <td colspan="4" class="text-center text-gray-500 py-12">
                                        <p class="text-lg">No data maps found.</p>
                                        <p class="text-sm">Get started by creating a new data map.</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div v-if="pagination.total > 0" class="mt-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <span>Per Page:</span>
                            <select id="perPage" v-model="perPage" @change="fetchDataMaps(1, $event.target.value)" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-1">
                                <option v-for="option in perPageOptions" :key="option" :value="option">{{ option }}</option>
                            </select>
                            <span class="hidden sm:inline">| Total Maps: {{ pagination.total }}</span>
                        </div>
                        <nav v-if="pagination.lastPage > 1" class="flex items-center justify-center">
                            <button @click="fetchDataMaps(pagination.currentPage - 1, perPage)" :disabled="pagination.currentPage <= 1"
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                &laquo; Prev
                            </button>
                            <span class="px-4 py-1 text-sm text-gray-800 bg-gray-100 border-t border-b border-gray-300">
                                Page {{ pagination.currentPage }} of {{ pagination.lastPage }}
                            </span>
                            <button @click="fetchDataMaps(pagination.currentPage + 1, perPage)" :disabled="pagination.currentPage >= pagination.lastPage"
                                    class="px-3 py-1 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                                Next &raquo;
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <div v-if="showCreateEditModal" class="fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-lg shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col">
                <div class="p-6 border-b">
                    <h2 class="text-2xl font-bold">{{ isEditing ? 'Edit' : 'Create' }} Data Map</h2>
                </div>

                <div class="p-6 flex-grow overflow-y-auto">
                    <div v-if="isEditing" class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-6">
                            <button @click="modalTab = 'main'" :class="['py-3 px-1 border-b-2 font-medium text-sm', modalTab === 'main' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300']">
                                Main Settings
                            </button>
                            <button @click="modalTab = 'customFields'" :class="['py-3 px-1 border-b-2 font-medium text-sm', modalTab === 'customFields' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300']">
                                Custom Fields
                            </button>
                        </nav>
                    </div>

                    <form @submit.prevent="submitDataMap" id="dataMapForm">
                        <!-- Main Settings Tab -->
                        <div v-show="modalTab === 'main'" class="space-y-5">
                            <div>
                                <label :class="Label" for="name">Name</label>
                                <input id="name" v-model="form.name" :class="Input" type="text" required>
                                <div v-if="form.errors.name" class="text-red-500 text-sm mt-1">{{ form.errors.name[0] }}</div>
                            </div>
                            <div>
                                <label :class="Label" class="mb-3 block">Choose Icon</label>
                                <div class="grid grid-cols-4 sm:grid-cols-6 gap-3">
                                    <button v-for="icon in mapIcons" :key="icon.name" type="button" 
                                            @click="form.image = icon.name"
                                            :class="['group relative p-3 rounded-lg border-2 transition-all duration-200 hover:shadow-md', 
                                                   form.image === icon.name ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-white hover:border-gray-400']">
                                        <div class="flex flex-col items-center space-y-2">
                                            <MapIcon :name="icon.name" :size="24" 
                                                   :class="form.image === icon.name ? 'text-blue-600' : 'text-gray-600 group-hover:text-gray-800'" />
                                            <span :class="['text-xs font-medium text-center leading-tight', 
                                                         form.image === icon.name ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700']">
                                                {{ icon.label }}
                                            </span>
                                        </div>
                                    </button>
                                </div>
                                <div v-if="form.errors.image" class="text-red-500 text-sm mt-1">{{ form.errors.image[0] }}</div>
                            </div>
                            <div v-if="isEditing" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="p-3 border rounded-lg">
                                    <label :class="Label" class="mb-2 block">Visibility</label>
                                    <div class="flex items-center justify-between">
                                        <span class="flex items-center text-sm text-gray-700">
                                            <LockOpen v-if="form.visibility === 'public'" class="w-5 h-5 mr-2 text-green-600" />
                                            <Lock v-else class="w-5 h-5 mr-2 text-gray-500" />
                                            {{ form.visibility === 'public' ? 'Public' : 'Private' }}
                                        </span>
                                        <button type="button" @click="form.visibility = form.visibility === 'public' ? 'private' : 'public'" role="switch" :aria-checked="form.visibility === 'public'"
                                                :class="['peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                                                         form.visibility === 'public' ? 'bg-green-500' : 'bg-gray-300']">
                                            <span :class="['pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0 transition-transform',
                                                           form.visibility === 'public' ? 'translate-x-5' : 'translate-x-0']" />
                                        </button>
                                    </div>
                                </div>
                                <div class="p-3 border rounded-lg">
                                    <label :class="Label" class="mb-2 block">Status</label>
                                    <div class="flex items-center justify-between">
                                        <span class="flex items-center text-sm text-gray-700">
                                            <CircleCheck v-if="form.status === 'active'" class="w-5 h-5 mr-2 text-green-600" />
                                            <CircleX v-else class="w-5 h-5 mr-2 text-red-600" />
                                            {{ form.status === 'active' ? 'Active' : 'Inactive' }}
                                        </span>
                                        <button type="button" @click="form.status = form.status === 'active' ? 'inactive' : 'active'" role="switch" :aria-checked="form.status === 'active'"
                                                :class="['peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                                                         form.status === 'active' ? 'bg-green-500' : 'bg-red-500']">
                                            <span :class="['pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0 transition-transform',
                                                           form.status === 'active' ? 'translate-x-5' : 'translate-x-0']" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Fields Tab -->
                        <div v-if="isEditing" v-show="modalTab === 'customFields'" class="space-y-4">
                            <div v-for="(field, index) in form.customFields" :key="index" class="p-3 border rounded-lg bg-gray-50/50 relative">
                                <div v-if="field.default === 'yes'" class="absolute top-2 right-2 text-xs font-bold text-gray-500 bg-gray-200 px-2 py-1 rounded-full">Default</div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
                                    <div>
                                        <label class="text-xs text-gray-500 block mb-1">Field Name</label>
                                        <input v-model="field.name" placeholder="Field Name" :class="[Input, field.default === 'yes' ? 'bg-gray-100 cursor-not-allowed' : '']" required :disabled="field.default === 'yes'">
                                    </div>
                                    <div>
                                        <label class="text-xs text-gray-500 block mb-1">Field Type</label>
                                        <select v-model="field.type" :class="[Select, field.default === 'yes' ? 'bg-gray-100 cursor-not-allowed' : '']" :disabled="field.default === 'yes'">
                                            <option value="text">Text</option>
                                            <option value="number">Number</option>
                                            <option value="date">Date</option>
                                            <option value="subItems">Sub-Items</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="text-xs text-gray-500 block mb-1">Required</label>
                                        <select v-model="field.isRequired" :class="[Select, field.default === 'yes' ? 'bg-gray-100 cursor-not-allowed' : '']" :disabled="field.default === 'yes'">
                                            <option value="yes">Required</option>
                                            <option value="no">Optional</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="flex justify-end mt-2" v-if="field.default !== 'yes'">
                                        <button type="button" @click="removeCustomField(index)" :class="`${ButtonDestructive} px-2 py-1 text-xs`">Remove Field</button>
                                </div>

                                <!-- Sub-items for custom fields -->
                                <div v-if="field.type === 'subItems'" class="mt-4 pt-3 pl-4 border-t">
                                    <h4 class="text-base font-semibold text-gray-800 mb-2">Sub-Item Fields</h4>
                                    
                                    <div v-if="field.default === 'yes'">
                                        <div v-for="(subField, subIndex) in field.subFields" :key="subIndex" class="grid grid-cols-1 md:grid-cols-3 gap-2 items-center mb-2 p-2 border rounded-md bg-white">
                                            <input :value="subField.name" :class="[Input, 'bg-gray-100']" disabled>
                                            <select :value="subField.type" :class="[Select, 'bg-gray-100']" disabled>
                                                <option value="text">Text</option>
                                                <option value="number">Number</option>
                                                <option value="date">Date</option>
                                            </select>
                                            <select :value="subField.isRequired" :class="[Select, 'bg-gray-100']" disabled>
                                                <option value="yes">Required</option>
                                                <option value="no">Optional</option>
                                            </select>
                                        </div>
                                            <p v-if="!field.subFields || field.subFields.length === 0" class="text-sm text-gray-500 italic">This default field has no configurable sub-items.</p>
                                    </div>

                                    <div v-else>
                                        <div v-for="(subField, subIndex) in field.subFields" :key="subIndex" class="grid grid-cols-1 md:grid-cols-4 gap-2 items-center mb-2">
                                            <input v-model="subField.name" placeholder="Sub-Field Name" :class="Input" required>
                                            <select v-model="subField.type" :class="Select">
                                                <option value="text">Text</option>
                                                <option value="number">Number</option>
                                                <option value="date">Date</option>
                                            </select>
                                            <select v-model="subField.isRequired" :class="Select">
                                                <option value="yes">Required</option>
                                                <option value="no">Optional</option>
                                            </select>
                                            <button type="button" @click="removeSubField(index, subIndex)" :class="`${ButtonDestructive} px-2 py-1`">Remove</button>
                                        </div>
                                        <button type="button" @click="addSubField(index)" :class="`${ButtonSecondary} px-3 py-1 mt-2`">Add Sub-Field</button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" @click="addCustomField" :class="`${ButtonSecondary} px-3 py-1 mt-4`">Add New Custom Field</button>
                        </div>
                    </form>
                </div>

                <div class="p-4 bg-gray-50 border-t flex justify-end space-x-4">
                    <button type="button" @click="showCreateEditModal = false" :class="`${ButtonSecondary} px-4 py-2`">Cancel</button>
                    <button type="submit" form="dataMapForm" :disabled="form.processing" :class="`${ButtonPrimary} px-4 py-2`">{{ form.processing ? 'Saving...' : (isEditing ? 'Update' : 'Create') }}</button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>