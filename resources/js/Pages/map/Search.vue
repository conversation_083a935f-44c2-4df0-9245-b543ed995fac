<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import axios from 'axios';
import { debounce } from 'lodash';
import AppLayout from '@/Layouts/AppLayout.vue';

const props = defineProps({
    provinces: { type: Array, default: () => [] },
});

// --- STATE REFS ---
const mapContainer = ref(null);
const map = ref(null);
const searchMode = ref(localStorage.getItem('searchMode') || 'text'); // 'text' or 'coordinates'
const searchQuery = ref('');
const coordinateForm = ref({
    latitude: '',
    longitude: '',
});
const selectedLanguage = ref(localStorage.getItem('mapLanguage') || 'en');
const selectedFilter = ref(localStorage.getItem('mapFilter') || 'all');
const panelsVisible = ref(true);
const selectedTheme = ref(localStorage.getItem('mapTheme') || 'Default');

const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);
const coordinateSearchResults = ref([]);
const lastSearchedCoords = ref(null);

const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);
const searchLayerIds = ref([]);
const searchSourceIds = ref([]);
const selectedResultId = ref(null);
const selectedResultType = ref(null);

// --- PRIVATE MAP VARIABLES ---
let hoverPopup = null;
let selectedPopup = null;
let hoveredFeature = { id: null, sourceId: null, type: null };
let clickMarker = null;

// --- CONFIGURATION ---
const MAP_CONFIG = {
    center: [29.8739, -1.9403],
    zoom: 8.5,
    minSearchChars: 3,
    debounceMs: 300,
    fitBoundsPadding: { top: 200, bottom: 200, left: 200, right: 200 },
    maxZoomForFit: 14,
};

const UI_CONFIG = {
    languages: [
        { code: 'rw', name: 'Kinyarwanda' },
        { code: 'en', name: 'English' },
        { code: 'fr', name: 'Français' },
    ],
    filters: [
        { code: 'all', name: 'All', icon: 'M12 4v16m8-8H4' },
        { code: 'district', name: 'District', icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4' },
        { code: 'sector', name: 'Sector', icon: 'M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7' },
        { code: 'cell', name: 'Cell', icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2' },
        { code: 'village', name: 'Village', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
        { code: 'health_fac', name: 'Health Facility', icon: 'M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z' },
        { code: 'pattern', name: 'Pattern', icon: 'M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z' },
    ],
    layerStyles: {
        province: { fillColor: '#3b82f6', borderColor: '#2563eb', fillOpacity: 0.15, borderWidth: 1.5 },
        district: { fillColor: '#22c55e', borderColor: '#16a34a', fillOpacity: 0.2, borderWidth: 1.5 },
        sector: { fillColor: '#a855f7', borderColor: '#9333ea', fillOpacity: 0.2, borderWidth: 1 },
        cell: { fillColor: '#f97316', borderColor: '#ea580c', fillOpacity: 0.2, borderWidth: 0.8 },
        village: { fillColor: '#ec4899', borderColor: '#db2777', fillOpacity: 0.2, borderWidth: 0.5 },
        healthFac: { fillColor: '#DC2626', borderColor: '#B91C1C' },
        searchResult: { fillColor: '#ec4899', borderColor: '#db2777', fillOpacity: 0.2, borderWidth: 0.5 },
        search_center: { fillColor: '#f59e0b', borderColor: '#d97706' },
    },
    highlightStyle: { color: '#ffdd00', width: 3, opacity: 0.9 },
    highlightSourceId: 'highlight-source',
    highlightLayerId: 'highlight-layer',
    mapThemes: {
        'Default': {
            url: 'https://a.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            paint: { 'raster-saturation': -0.8, 'raster-contrast': 0.2, 'raster-opacity': 0.9 }
        },
        'Satellite': {
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            paint: {}
        }
    },
};

// --- COMPUTED PROPERTIES ---
const totalResults = computed(() => {
    if (searchMode.value === 'coordinates') {
        return coordinateSearchResults.value?.length || 0;
    }
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const canSearch = computed(() => {
    if (searchMode.value === 'coordinates') {
        const lat = parseFloat(coordinateForm.value.latitude);
        const lon = parseFloat(coordinateForm.value.longitude);
        return !isNaN(lat) && !isNaN(lon) && lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
    }
    return searchQuery.value.trim().length >= MAP_CONFIG.minSearchChars;
});

// --- LOGIC FUNCTIONS ---
const performTextSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < MAP_CONFIG.minSearchChars) {
        clearSearch(false);
        return;
    }
    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();
    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });
        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || `Failed to fetch search results.`;
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
        updateMapLayers();
    }
}, MAP_CONFIG.debounceMs);

const performCoordinateSearch = async (latitude, longitude) => {
    if (!canSearch.value) {
        clearSearch(false);
        return;
    }
    isLoading.value = true;
    error.value = null;
    lastSearchedCoords.value = { latitude, longitude };
    const startTime = performance.now();
    try {
        const { data } = await axios.post('/map/search-latitude-langitude-json', {
            latitude,
            longitude,
            lang: selectedLanguage.value,
        });
        coordinateSearchResults.value = (data || []).map(item => ({
            ...item,
            geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
        }));
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || `Failed to fetch search results.`;
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
        updateMapLayers();
    }
};

const submitCoordinateSearch = () => {
    if (canSearch.value) {
        performCoordinateSearch(coordinateForm.value.latitude, coordinateForm.value.longitude);
    } else {
        error.value = "Please enter valid latitude (-90 to 90) and longitude (-180 to 180).";
    }
};

const clearSearch = (resetInputs = true) => {
    if (resetInputs) {
        if (searchMode.value === 'text') {
            searchQuery.value = '';
        } else {
            coordinateForm.value.latitude = '';
            coordinateForm.value.longitude = '';
            lastSearchedCoords.value = null;
        }
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    coordinateSearchResults.value = [];
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
    clearSelection();
    updateMapLayers();
};

const updateMapLayers = () => {
    if (!map.value?.isStyleLoaded()) return;

    searchLayerIds.value.forEach(layerId => {
        if (map.value.getLayer(layerId)) map.value.removeLayer(layerId);
    });
    searchSourceIds.value.forEach(sourceId => {
        if (map.value.getSource(sourceId)) map.value.removeSource(sourceId);
    });
    searchLayerIds.value = [];
    searchSourceIds.value = [];
    clearHighlight();

    const baseLayerVisibility = totalResults.value > 0 || canSearch.value ? 'none' : 'visible';
    props.provinces.forEach(p => {
        if (p?.id && map.value.getSource(`province-source-${p.id}`)) {
            ['fill', 'border'].forEach(suffix => {
                const layerId = `province-${suffix}-${p.id}`;
                if (map.value.getLayer(layerId)) map.value.setLayoutProperty(layerId, 'visibility', baseLayerVisibility);
            });
        }
    });

    const allFeaturesForZoom = [];
    if (totalResults.value > 0) {
        if (searchMode.value === 'text') {
            ALL_RESULT_TYPES.forEach(type => {
                searchResults.value[type].forEach(result => {
                    const featureType = type.slice(0, -1);
                    if (result.geojson) {
                        const feature = { type: "Feature", geometry: result.geojson, properties: { ...result, id: result.id, type: featureType } };
                        addGeoJsonLayer(featureType, result.id, feature, true);
                        allFeaturesForZoom.push(feature);
                    } else if (result.latitude && result.longitude) {
                        const pointFeature = { type: "Feature", geometry: { type: "Point", coordinates: [result.longitude, result.latitude] }, properties: { ...result, id: result.id, type: featureType } };
                        addPointMarker(featureType, result.id, pointFeature, true);
                        allFeaturesForZoom.push(pointFeature);
                    }
                });
            });
            if (searchQuery.value.trim()) zoomToResults(allFeaturesForZoom);
        } else {
            // Coordinate search mode
            coordinateSearchResults.value.forEach(result => {
                if (result.geojson) {
                    const feature = { type: "Feature", geometry: result.geojson, properties: { ...result, id: result.id, type: 'searchResult' } };
                    addGeoJsonLayer('searchResult', result.id, feature, true);
                    allFeaturesForZoom.push(feature);
                }
            });

            if (lastSearchedCoords.value) {
                const { latitude, longitude } = lastSearchedCoords.value;
                const pointFeature = { type: "Feature", geometry: { type: "Point", coordinates: [parseFloat(longitude), parseFloat(latitude)] }, properties: { id: 'search-center', type: 'search_center' } };
                addPointMarker('search_center', 'search-center-id', pointFeature, true);
                allFeaturesForZoom.push(pointFeature);
            }

            if (allFeaturesForZoom.length > 0) zoomToResults(allFeaturesForZoom);
        }
    } else if ((searchMode.value === 'text' && !searchQuery.value.trim()) ||
               (searchMode.value === 'coordinates' && !coordinateForm.value.latitude && !coordinateForm.value.longitude)) {
        map.value.flyTo({ center: MAP_CONFIG.center, zoom: MAP_CONFIG.zoom, duration: 1000 });
    }
};

const zoomToResults = (features) => {
    if (!map.value || features.length === 0) return;
    const bounds = new maplibregl.LngLatBounds();
    let hasValidGeometry = false;
    features.forEach(feature => {
        if (feature.geometry?.type === 'Point' && feature.geometry.coordinates) {
            bounds.extend(feature.geometry.coordinates);
            hasValidGeometry = true;
        } else if (feature.geometry?.bbox) {
            bounds.extend(feature.geometry.bbox);
            hasValidGeometry = true;
        } else if (feature.geometry?.coordinates) {
            const extendCoordinates = (coords) => {
                if (Array.isArray(coords[0])) coords.forEach(extendCoordinates);
                else bounds.extend(coords);
            };
            extendCoordinates(feature.geometry.coordinates);
            hasValidGeometry = true;
        }
    });
    if (hasValidGeometry && !bounds.isEmpty()) {
        map.value.fitBounds(bounds, { padding: MAP_CONFIG.fitBoundsPadding, maxZoom: MAP_CONFIG.maxZoomForFit, duration: 1000 });
    } else if (features.length === 1 && features[0].geometry?.type === 'Point') {
        map.value.flyTo({ center: features[0].geometry.coordinates, zoom: MAP_CONFIG.maxZoomForFit, duration: 800 });
    }
};

const selectResult = (type, id) => {
    let result;
    if (searchMode.value === 'coordinates') {
        result = coordinateSearchResults.value.find(r => r.id === id);
    } else {
        result = searchResults.value[`${type}s`]?.find(r => r.id === id);
    }
    if (result) {
        displayFeaturePopup(result, type);
        if (window.innerWidth < 640) { // sm breakpoint
            panelsVisible.value = false;
        }
    }
};

const showPanels = () => {
    panelsVisible.value = true;
};

const clearSelection = () => {
    selectedResultId.value = null;
    selectedResultType.value = null;
    if (selectedPopup) {
        selectedPopup.remove();
        selectedPopup = null;
    }
    clearHighlight();
};

const clearHighlight = () => {
    if (map.value?.getSource(UI_CONFIG.highlightSourceId)) {
        map.value.getSource(UI_CONFIG.highlightSourceId).setData({ type: 'FeatureCollection', features: [] });
    }
};

// --- MAP SETUP & EVENT HANDLING ---
const resizeMap = () => map.value?.resize();

const applyMapStyle = (themeName) => {
    if (!map.value || !map.value.isStyleLoaded()) return;
    const theme = UI_CONFIG.mapThemes[themeName];
    if (theme) {
        const source = map.value.getSource('osm');
        if (source) {
            source.setTiles([theme.url]);
        }

        // Update paint properties
        const paintProperties = theme.paint || UI_CONFIG.mapThemes['Default'].paint;
        for (const key in paintProperties) {
            map.value.setPaintProperty('osm-tiles', key, paintProperties[key]);
        }

        selectedTheme.value = themeName;
        localStorage.setItem('mapTheme', themeName);
    }
};

const clearMapStyle = () => {
    applyMapStyle('Default');
    localStorage.removeItem('mapTheme');
};

const getUserLocation = () => {
    if (!navigator.geolocation) {
        error.value = "Geolocation is not supported by your browser.";
        return;
    }

    isLoading.value = true;
    navigator.geolocation.getCurrentPosition(
        (position) => {
            coordinateForm.value.latitude = position.coords.latitude.toFixed(6);
            coordinateForm.value.longitude = position.coords.longitude.toFixed(6);
            if (clickMarker) clickMarker.remove();
            clickMarker = new maplibregl.Marker({ color: '#3b82f6' })
                .setLngLat([position.coords.longitude, position.coords.latitude])
                .addTo(map.value);
            submitCoordinateSearch();
        },
        (err) => {
            isLoading.value = false;
            error.value = `Failed to get location: ${err.message}`;
            console.error(err);
        }
    );
};

onMounted(() => {
    if (!mapContainer.value) {
        error.value = 'Map container element not found.';
        return;
    }
    const initialTheme = UI_CONFIG.mapThemes[selectedTheme.value] || UI_CONFIG.mapThemes['Default'];

    try {
        map.value = new maplibregl.Map({
            container: mapContainer.value,
            style: {
                version: 8,
                sources: { osm: { type: 'raster', tiles: [initialTheme.url], tileSize: 256, attribution: initialTheme.attribution } },
                layers: [{ id: 'osm-tiles', type: 'raster', source: 'osm', paint: initialTheme.paint }],
            },
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            attributionControl: false,
        });
        map.value.addControl(new maplibregl.NavigationControl(), 'top-right');
        map.value.addControl(new maplibregl.AttributionControl({ compact: true }), 'bottom-right');
        map.value.on('load', setupMap);
        window.addEventListener('resize', resizeMap);
    } catch (err) {
        console.error('Map initialization error:', err);
        error.value = 'Failed to initialize the map.';
    }
});

onUnmounted(() => {
    window.removeEventListener('resize', resizeMap);
    map.value?.remove();
});

const setupMap = () => {
    if (!map.value) return;
    if (Array.isArray(props.provinces)) {
        props.provinces.forEach(province => {
            if (province?.id && province.geojson) {
                const feature = {
                    type: "Feature",
                    geometry: typeof province.geojson === 'string' ? JSON.parse(province.geojson) : province.geojson,
                    properties: { ...province, id: province.id, type: 'province' }
                };
                if (typeof feature.properties.latitude === 'string') feature.properties.latitude = parseFloat(feature.properties.latitude);
                if (typeof feature.properties.longitude === 'string') feature.properties.longitude = parseFloat(feature.properties.longitude);
                addGeoJsonLayer('province', province.id, feature, false);
            }
        });
    }
    map.value.addSource(UI_CONFIG.highlightSourceId, { type: 'geojson', data: { type: 'FeatureCollection', features: [] } });
    map.value.addLayer({
        id: UI_CONFIG.highlightLayerId,
        type: 'line',
        source: UI_CONFIG.highlightSourceId,
        paint: { 'line-color': UI_CONFIG.highlightStyle.color, 'line-width': UI_CONFIG.highlightStyle.width, 'line-opacity': UI_CONFIG.highlightStyle.opacity },
    });

    applyMapStyle(selectedTheme.value);

    map.value.on('mousemove', handleMapHover);
    map.value.on('mouseleave', handleMapLeave);
    map.value.on('click', handleMapClick);
};

const handleMapLeave = () => {
    if (hoveredFeature.id !== null && hoveredFeature.sourceId && map.value.getSource(hoveredFeature.sourceId)) {
        map.value.setFeatureState({ source: hoveredFeature.sourceId, id: hoveredFeature.id }, { hover: false });
    }
    hoveredFeature = { id: null, sourceId: null, type: null };
    if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
    }
};

const handleMapHover = (e) => {
    if (!map.value?.isStyleLoaded()) return;
    const layersToQuery = [
        ...searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-'))),
        ...props.provinces.flatMap(p => [`province-fill-${p.id}`]).filter(id => map.value.getLayer(id))
    ].filter(id => !id.includes('-border-'));
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });
    map.value.getCanvas().style.cursor = features.length ? 'pointer' : '';
    if (hoveredFeature.id !== null && hoveredFeature.sourceId && map.value.getSource(hoveredFeature.sourceId)) {
        map.value.setFeatureState({ source: hoveredFeature.sourceId, id: hoveredFeature.id }, { hover: false });
    }
    if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
    }
    if (features.length > 0) {
        const topFeature = features[0];
        const featureId = topFeature.properties.id;
        const featureType = topFeature.properties.type;
        const sourceId = topFeature.layer.source;
        if (featureId !== undefined && sourceId && featureType) {
            map.value.setFeatureState({ source: sourceId, id: featureId }, { hover: true });
            hoveredFeature = { id: featureId, sourceId: sourceId, type: featureType };
        }
        const displayName = getDisplayName(topFeature.properties);
        if (displayName) {
            hoverPopup = new maplibregl.Popup({ closeButton: false, anchor: 'bottom-left', offset: [5, -5], className: 'hover-popup' })
                .setLngLat(e.lngLat)
                .setHTML(`<div class="hover-popup-content"><div class="hover-item"><div class="hover-indicator" style="background-color: ${UI_CONFIG.layerStyles[hoveredFeature.type]?.fillColor || '#ccc'}"></div><span>${displayName}</span></div></div>`)
                .addTo(map.value);
        }
    }
};

const handleMapClick = (e) => {
    if (!map.value) return;

    const layersToQuery = [
        ...searchLayerIds.value.filter(id => map.value.getLayer(id) && (id.includes('-fill-') || id.includes('-point-'))),
        ...props.provinces.flatMap(p => [`province-fill-${p.id}`]).filter(id => map.value.getLayer(id))
    ];
    const features = map.value.queryRenderedFeatures(e.point, { layers: layersToQuery });

    if (features.length > 0) {
        const topFeature = features[0];
        const type = topFeature.properties.type;
        const id = topFeature.properties.id;
        if (id === undefined || !type) return;

        let result;
        if (searchMode.value === 'coordinates' && type === 'searchResult') {
            result = coordinateSearchResults.value.find(r => r.id == id);
        } else if (type !== 'search_center') {
            result = searchResults.value[`${type}s`]?.find(r => r.id == id);
            if (!result && type === 'province') {
                result = props.provinces.find(p => p.id == id);
                if (result) {
                    if (typeof result.geojson === 'string') result.geojson = JSON.parse(result.geojson);
                    if (typeof result.latitude === 'string') result.latitude = parseFloat(result.latitude);
                    if (typeof result.longitude === 'string') result.longitude = parseFloat(result.longitude);
                }
            }
        }
        if (result) {
            displayFeaturePopup(result, type);
            return;
        }
    }

    // Handle coordinate search mode map clicks
    if (searchMode.value === 'coordinates') {
        const { lng, lat } = e.lngLat;
        coordinateForm.value.latitude = lat.toFixed(6);
        coordinateForm.value.longitude = lng.toFixed(6);

        if (clickMarker) {
            clickMarker.remove();
        }
        clickMarker = new maplibregl.Marker({ color: '#3b82f6' })
            .setLngLat([lng, lat])
            .addTo(map.value);

        submitCoordinateSearch();
    }
};

// --- MAP LAYER HELPERS ---
const addGeoJsonLayer = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    const sourceId = `${type}-source-${id}`;
    if (map.value.getSource(sourceId)) return;
    const layerStyle = UI_CONFIG.layerStyles[type];
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    map.value.addLayer({
        id: `${type}-fill-${id}`, type: 'fill', source: sourceId,
        paint: { 'fill-color': layerStyle.fillColor, 'fill-opacity': ['case', ['boolean', ['feature-state', 'hover'], false], 0.6, layerStyle.fillOpacity] },
    });
    map.value.addLayer({
        id: `${type}-border-${id}`, type: 'line', source: sourceId,
        paint: { 'line-color': layerStyle.borderColor, 'line-width': ['case', ['boolean', ['feature-state', 'hover'], false], layerStyle.borderWidth + 1, layerStyle.borderWidth] },
    });
    if (trackLayer) {
        searchLayerIds.value.push(`${type}-fill-${id}`, `${type}-border-${id}`);
        searchSourceIds.value.push(sourceId);
    }
};

const addPointMarker = (type, id, feature, trackLayer) => {
    if (!map.value || !feature?.geometry) return;
    const sourceId = `${type}-point-source-${id}`;
    if (map.value.getSource(sourceId)) return;
    const layerStyle = UI_CONFIG.layerStyles[type];
    map.value.addSource(sourceId, { type: 'geojson', data: feature, promoteId: 'id' });
    map.value.addLayer({
        id: `${type}-point-${id}`, type: 'circle', source: sourceId,
        paint: {
            'circle-color': layerStyle.fillColor || '#FF0000',
            'circle-radius': ['case', ['boolean', ['feature-state', 'hover'], false], 8, 6],
            'circle-stroke-color': layerStyle.borderColor || '#FFFFFF',
            'circle-stroke-width': ['case', ['boolean', ['feature-state', 'hover'], false], 2, 1.5],
            'circle-opacity': 0.9,
        },
    });
    if (trackLayer) {
        searchLayerIds.value.push(`${type}-point-${id}`);
        searchSourceIds.value.push(sourceId);
    }
};

const displayFeaturePopup = (feature, type) => {
    if (!map.value) return;
    clearSelection();
    selectedResultId.value = feature.id;
    selectedResultType.value = type;
    const highlightSource = map.value.getSource(UI_CONFIG.highlightSourceId);
    if (highlightSource) {
        if (feature.geojson) highlightSource.setData(feature.geojson);
        else highlightSource.setData({ type: 'FeatureCollection', features: [] });
    }
    const displayName = getDisplayName(feature);
    const popupContent = `
        <div class="selected-popup">
            <div class="popup-header">
                <div class="popup-indicator" style="background-color: ${UI_CONFIG.layerStyles[type]?.fillColor || '#ccc'}"></div>
                <h3>${displayName}</h3>
            </div>
            <div class="popup-content">
                <p class="popup-type">${type.charAt(0).toUpperCase() + type.slice(1)}</p>
                ${feature.address ? `<p class="popup-address">${feature.address}</p>` : ''}
                ${feature.code ? `<p class="popup-code">Code: ${feature.code}</p>` : ''}
                ${typeof feature.latitude === 'number' && typeof feature.longitude === 'number' ? `<p class="popup-coords">Lat: ${feature.latitude.toFixed(4)}, Lng: ${feature.longitude.toFixed(4)}</p>` : ''}
            </div>
        </div>`;
    const bounds = new maplibregl.LngLatBounds();
    let targetLngLat;
    if (feature.geojson?.bbox) {
        bounds.extend(feature.geojson.bbox);
        targetLngLat = bounds.getCenter();
    } else if (typeof feature.latitude === 'number' && typeof feature.longitude === 'number') {
        targetLngLat = [feature.longitude, feature.latitude];
        bounds.extend(targetLngLat);
    } else {
        targetLngLat = map.value.getCenter();
    }
    if (targetLngLat) {
        selectedPopup = new maplibregl.Popup({ closeButton: true, closeOnClick: false, anchor: 'bottom', offset: [0, -10] })
            .setLngLat(targetLngLat)
            .setHTML(popupContent)
            .addTo(map.value);
        selectedPopup.on('close', clearSelection);
        if (!bounds.isEmpty() && JSON.stringify(bounds.getNorthEast()) !== JSON.stringify(bounds.getSouthWest())) {
            map.value.fitBounds(bounds, { padding: 200, maxZoom: 14, duration: 800 });
        } else {
            map.value.flyTo({ center: targetLngLat, zoom: 14, duration: 800 });
        }
    }
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

const getPlaceholderText = () => ({
    rw: 'Shakisha ahantu...',
    en: 'Search for a location...',
    fr: 'Rechercher un lieu...',
}[selectedLanguage.value] || 'Search locations...');

// --- WATCHERS ---
watch(searchQuery, (newQuery) => {
    if (searchMode.value === 'text') {
        performTextSearch(newQuery, selectedLanguage.value, selectedFilter.value);
    }
});

watch(selectedLanguage, (newLang) => {
    localStorage.setItem('mapLanguage', newLang);
    if (searchMode.value === 'text') {
        performTextSearch(searchQuery.value, newLang, selectedFilter.value);
    } else if (canSearch.value) {
        performCoordinateSearch(coordinateForm.value.latitude, coordinateForm.value.longitude);
    }
});

watch(selectedFilter, (newFilter) => {
    localStorage.setItem('mapFilter', newFilter);
    if (searchMode.value === 'text') {
        performTextSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});

watch(searchMode, (newMode) => {
    localStorage.setItem('searchMode', newMode);
    clearSearch(true);
});

</script>

<template>
    <AppLayout title="Rwanda Geo Fullscreen">
        <div class="relative w-screen h-screen font-sans">
            <!-- Map Container -->
            <main ref="mapContainer" class="w-full h-full"></main>

            <!-- Floating Panels Container -->
            <div v-if="!panelsVisible" class="absolute top-4 left-4 z-20 sm:hidden pointer-events-auto">
                <Button @click="showPanels">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </Button>
            </div>

            <div v-if="panelsVisible" class="absolute top-0 left-0 z-10 h-full w-full sm:w-auto p-2 sm:p-4 flex flex-col gap-4 pointer-events-none">
                <!-- Search & Controls Panel -->
                <div class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto">
                    <div class="p-4 space-y-4">
                        <!-- Modern Toggle Switch -->
                        <div class="flex items-center justify-center">
                            <div class="bg-gray-100 p-1 rounded-full flex items-center">
                                <button
                                    @click="searchMode = 'text'"
                                    :class="[
                                        'px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out',
                                        searchMode === 'text'
                                            ? 'bg-white text-gray-900 shadow-sm'
                                            : 'text-gray-600 hover:text-gray-800'
                                    ]"
                                >
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                    </svg>
                                    Text Search
                                </button>
                                <button
                                    @click="searchMode = 'coordinates'"
                                    :class="[
                                        'px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ease-in-out',
                                        searchMode === 'coordinates'
                                            ? 'bg-white text-gray-900 shadow-sm'
                                            : 'text-gray-600 hover:text-gray-800'
                                    ]"
                                >
                                    <svg class="w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    Coordinates
                                </button>
                            </div>
                        </div>

                        <!-- Text Search Interface -->
                        <div v-if="searchMode === 'text'" class="flex items-center gap-2">
                            <div class="relative flex-1">
                                <svg class="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                </svg>
                                <Input v-model="searchQuery" :placeholder="getPlaceholderText()" class="w-full pl-10 pr-10 py-2.5 text-base rounded-lg border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white" />
                                <div v-if="isLoading" class="absolute right-3 top-1/2 -translate-y-1/2">
                                    <svg class="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Coordinate Search Interface -->
                        <div v-if="searchMode === 'coordinates'" class="space-y-3">
                            <div class="grid grid-cols-2 gap-2">
                                <div>
                                    <label for="latitude" class="text-xs font-medium text-gray-600">Latitude</label>
                                    <Input id="latitude" v-model="coordinateForm.latitude" type="number" placeholder="-1.9403" class="w-full" @keyup.enter="submitCoordinateSearch"/>
                                </div>
                                <div>
                                    <label for="longitude" class="text-xs font-medium text-gray-600">Longitude</label>
                                    <Input id="longitude" v-model="coordinateForm.longitude" type="number" placeholder="29.8739" class="w-full" @keyup.enter="submitCoordinateSearch"/>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <Button @click="submitCoordinateSearch" class="flex-1" :disabled="isLoading || !canSearch">
                                    <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Search
                                </Button>
                                <Button @click="getUserLocation" variant="outline" :disabled="isLoading">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </Button>
                            </div>
                        </div>

                        <Dialog>
                            <DialogTrigger as-child>
                                <Button variant="outline" class="w-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" />
                                    </svg>
                                    Filters & Settings
                                </Button>
                            </DialogTrigger>
                            <DialogContent class="sm:max-w-[425px]">
                                <DialogHeader>
                                    <DialogTitle>Settings</DialogTitle>
                                    <DialogDescription>
                                        Select language{{ searchMode === 'text' ? ', filter by category,' : '' }} and customize map style.
                                    </DialogDescription>
                                </DialogHeader>
                                <div class="grid gap-4 py-4">
                                    <div class="space-y-2">
                                        <label class="text-xs font-semibold text-gray-600 uppercase">Language</label>
                                        <div class="flex items-center bg-gray-100 p-1 rounded-lg">
                                            <button
                                                v-for="lang in UI_CONFIG.languages"
                                                :key="lang.code"
                                                @click="selectedLanguage = lang.code"
                                                :class="[
                                                    'flex-1 text-center px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200',
                                                    selectedLanguage === lang.code ? 'bg-white text-blue-600 shadow-sm ring-1 ring-blue-200' : 'text-gray-600 hover:text-gray-800',
                                                ]"
                                            >
                                                {{ lang.name }}
                                            </button>
                                        </div>
                                    </div>
                                    <div v-if="searchMode === 'text'" class="space-y-2">
                                        <label class="text-xs font-semibold text-gray-600 uppercase">Filter</label>
                                        <div class="grid grid-cols-3 sm:grid-cols-4 gap-1.5">
                                            <button
                                                v-for="filter in UI_CONFIG.filters"
                                                :key="filter.code"
                                                @click="selectedFilter = filter.code"
                                                :title="filter.name"
                                                :class="[
                                                    'p-2 text-xs font-medium rounded-lg transition-all duration-200 flex flex-col items-center justify-center gap-1 aspect-square',
                                                    selectedFilter === filter.code ? 'bg-gray-800 text-white shadow-sm' : 'bg-gray-100 text-gray-600 hover:bg-gray-200',
                                                ]"
                                            >
                                                <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="filter.icon" />
                                                </svg>
                                                <span class="text-[10px] leading-tight">{{ filter.name }}</span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <label class="text-xs font-semibold text-gray-600 uppercase">Map Style</label>
                                        <div class="grid grid-cols-2 gap-2">
                                            <button
                                                v-for="(_, name) in UI_CONFIG.mapThemes"
                                                :key="name"
                                                @click="applyMapStyle(name)"
                                                :class="[
                                                    'p-2 text-xs font-medium rounded-lg transition-all duration-200 flex flex-col items-center justify-center gap-1',
                                                    selectedTheme === name ? 'ring-2 ring-blue-500' : ''
                                                ]"
                                            >
                                                <div class="w-12 h-8 rounded-md border" :style="{ backgroundColor: name === 'Default' ? '#f0f0f0' : '#34d399' }"></div>
                                                <span>{{ name }}</span>
                                            </button>
                                        </div>
                                        <Button variant="outline" size="sm" @click="clearMapStyle" class="mt-2">Reset Style</Button>
                                    </div>
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>

                <!-- Search Results Panel -->
                <div v-if="((searchMode === 'text' && searchQuery) || (searchMode === 'coordinates' && (canSearch || totalResults > 0 || error))) || totalResults > 0" class="w-full sm:w-80 md:w-96 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 pointer-events-auto flex flex-col flex-1 min-h-0">
                    <div class="px-4 py-2 border-b border-gray-200/80">
                        <div class="text-sm text-gray-600">
                            <span v-if="searchMode === 'text' && searchQuery.trim().length > 0 && !canSearch">Enter at least {{ MAP_CONFIG.minSearchChars }} characters.</span>
                            <span v-else-if="isLoading">Searching...</span>
                            <span v-else-if="totalResults > 0">{{ totalResults }} result{{ totalResults !== 1 ? 's' : '' }} <span v-if="searchTime > 0">found in {{ searchTime }}ms</span></span>
                            <span v-else-if="searchMode === 'coordinates' && lastSearchedCoords">No results found.</span>
                        </div>
                    </div>

                    <div class="flex-1 overflow-y-auto">
                        <div v-if="error" class="p-4 m-2 text-center text-red-700 bg-red-50 rounded-lg border border-red-200">
                            <p class="font-semibold">An Error Occurred</p>
                            <p class="text-sm mt-1">{{ error }}</p>
                        </div>
                        <div v-else-if="!isLoading && totalResults === 0 && ((searchMode === 'text' && canSearch) || (searchMode === 'coordinates' && lastSearchedCoords))" class="p-6 text-center text-gray-500">
                            <h3 class="font-medium text-gray-900">No results found</h3>
                            <p class="mt-1 text-sm">{{ searchMode === 'text' ? 'Try a different search term.' : 'Try different coordinates.' }}</p>
                        </div>
                        <!-- Text Search Results -->
                        <ul v-else-if="totalResults > 0 && searchMode === 'text'" class="space-y-1 p-2">
                            <template v-for="type in ALL_RESULT_TYPES" :key="type">
                                <li v-for="result in searchResults[type]" :key="`${type}-${result.id}`" @click="selectResult(type.slice(0, -1), result.id)" :class="['p-2.5 hover:bg-blue-50 rounded-lg cursor-pointer transition-colors duration-150 group', selectedResultId === result.id && selectedResultType === type.slice(0, -1) ? 'bg-blue-100 ring-2 ring-blue-200' : '']">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex-shrink-0 w-3 h-3" :class="{'rounded-full': !result.geojson, 'rounded-sm': result.geojson}" :style="{ backgroundColor: UI_CONFIG.layerStyles[type.slice(0, -1)]?.fillColor || '#ccc' }"></div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 truncate group-hover:text-blue-800">{{ getDisplayName(result) }}</p>
                                            <p class="text-xs text-gray-500 capitalize truncate">{{ result.address || (result.latitude && result.longitude ? 'Coordinates' : result.code || type.slice(0, -1)) }}</p>
                                        </div>
                                    </div>
                                </li>
                            </template>
                        </ul>
                        <!-- Coordinate Search Results -->
                        <ul v-else-if="totalResults > 0 && searchMode === 'coordinates'" class="space-y-1 p-2">
                            <li v-for="result in coordinateSearchResults" :key="`result-${result.id}`" @click="selectResult('searchResult', result.id)" :class="['p-2.5 hover:bg-blue-50 rounded-lg cursor-pointer transition-colors duration-150 group', selectedResultId === result.id && selectedResultType === 'searchResult' ? 'bg-blue-100 ring-2 ring-blue-200' : '']">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 w-3 h-3 rounded-sm" :style="{ backgroundColor: UI_CONFIG.layerStyles.searchResult.fillColor }"></div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate group-hover:text-blue-800">{{ getDisplayName(result) }}</p>
                                        <p class="text-xs text-gray-500 capitalize truncate">{{ result.address }}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<style>
/* Ensure html, body, and app root are full height */
html, body, #app {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent body scroll */
}

.maplibregl-ctrl-group {
    background-color: white !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Custom styles for popups */
.maplibregl-popup-content {
    padding: 0;
    background: transparent;
    box-shadow: none;
}
.maplibregl-popup.hover-popup .maplibregl-popup-content {
    background-color: rgba(30, 41, 59, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    font-size: 13px;
    max-width: 240px;
}
.maplibregl-popup-anchor-bottom .maplibregl-popup-tip { border-top-color: rgba(30, 41, 59, 0.9); }
.maplibregl-popup-anchor-top .maplibregl-popup-tip { border-bottom-color: rgba(30, 41, 59, 0.9); }
.maplibregl-popup-anchor-left .maplibregl-popup-tip { border-right-color: rgba(30, 41, 59, 0.9); }
.maplibregl-popup-anchor-right .maplibregl-popup-tip { border-left-color: rgba(30, 41, 59, 0.9); }

.selected-popup {
    background-color: rgba(255, 255, 255, 0.95);
    color: #1f2937;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
    border: 1px solid #e5e7eb;
    font-size: 14px;
    max-width: 280px;
}
.selected-popup .popup-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}
.selected-popup .popup-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    flex-shrink: 0;
}
.selected-popup h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}
.selected-popup .popup-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.selected-popup .popup-type, .selected-popup .popup-address, .selected-popup .popup-code, .selected-popup .popup-coords {
    font-size: 12px;
    color: #6b7280;
}

.hover-popup-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.hover-item {
    display: flex;
    align-items: center;
    gap: 6px;
}
.hover-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}
</style>