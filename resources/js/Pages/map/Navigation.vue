<script setup>
import { Head } from '@inertiajs/vue3';
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';

const props = defineProps({
    provinces: {
        type: Array,
        required: true,
        default: () => [],
    },
    districts: {
        type: Array,
        required: true,
        default: () => [],
    },
});


</script>

<template>
    <Head title="Navigation" />
    <div class="w-full h-screen">
        <div ref="mapContainer" class="w-full h-full"></div>
    </div>
</template>
