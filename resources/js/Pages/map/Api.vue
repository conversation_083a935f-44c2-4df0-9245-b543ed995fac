<template>
  <AppLayout title="Geocode Rwanda">
    <!-- Hero Terminal Section -->
    <section class="relative min-h-screen flex items-center justify-center bg-white">
      <!-- Background Grid -->
      <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>
      </div>

      <div class="relative max-w-7xl mx-auto px-6 w-full">
        <!-- Header -->
        <div class="text-center mb-12">
          <h1 class="text-5xl md:text-6xl font-bold text-black mb-4">
            API Playground
          </h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Test our geocoding API in real-time. Watch the magic happen as you type.
          </p>
        </div>

        <!-- Main Terminal Container -->
        <div class="bg-white border-2 border-black shadow-2xl max-w-6xl mx-auto">
          <!-- Terminal Header -->
          <div class="bg-black px-6 py-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="flex space-x-2">
                <div class="w-3 h-3 bg-white rounded-full"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div class="w-3 h-3 bg-gray-600 rounded-full"></div>
              </div>
              <span class="text-white font-mono text-sm">rwanda-geo-terminal</span>
            </div>
            <div class="flex items-center space-x-2 text-white text-sm">
              <span class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
              <span class="font-mono">online</span>
            </div>
          </div>

          <!-- Terminal Content Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">
            <!-- Left Panel: Input -->
            <div class="p-8 border-r border-gray-200">
              <div class="space-y-6">
                <!-- Search Input -->
                <div class="space-y-3">
                  <label class="text-sm font-bold text-black uppercase tracking-wider">Query Address:</label>
                  <div class="relative">
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 font-mono text-sm">$</div>
                    <input
                      v-model="searchQuery"
                      type="text"
                      class="w-full pl-8 pr-4 py-3 border-2 border-gray-300 bg-white text-black placeholder-gray-400 focus:border-black focus:outline-none font-mono"
                      placeholder="kigali, gasabo, kacyiru..."
                    />
                  </div>
                </div>

                <!-- Language & Filter -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="text-sm font-bold text-black uppercase tracking-wider">Language:</label>
                    <select
                      v-model="lang"
                      class="w-full mt-2 p-3 border-2 border-gray-300 bg-white text-black focus:border-black focus:outline-none font-mono"
                    >
                      <option value="en">EN</option>
                      <option value="fr">FR</option>
                      <option value="rw">RW</option>
                    </select>
                  </div>
                  <div>
                    <label class="text-sm font-bold text-black uppercase tracking-wider">Filter:</label>
                    <select
                      v-model="selectedFilter"
                      class="w-full mt-2 p-3 border-2 border-gray-300 bg-white text-black focus:border-black focus:outline-none font-mono"
                    >
                      <option value="all">ALL_DATA</option>
                      <option value="province">PROVINCE</option>
                      <option value="district">DISTRICT</option>
                      <option value="sector">SECTOR</option>
                      <option value="cell">CELL</option>
                      <option value="village">VILLAGE</option>
                      <option value="health_fac">HEALTH_FAC</option>
                      <option value="pattern">PATTERN</option>
                    </select>
                  </div>
                </div>

                <!-- Execute Button -->
                <button
                  @click="search"
                  :disabled="loading || !canPerformSearch"
                  :class="[
                    'w-full py-4 px-6 font-bold text-lg transition-all duration-300 border-2',
                    canPerformSearch && !loading
                      ? 'bg-black text-white border-black hover:bg-white hover:text-black'
                      : 'bg-gray-300 text-gray-500 border-gray-300 cursor-not-allowed'
                  ]"
                >
                  {{ loading ? 'EXECUTING...' : '→ EXECUTE_QUERY' }}
                </button>

                <!-- Status Indicators -->
                <div class="flex items-center space-x-4 text-sm font-mono">
                  <div class="flex items-center space-x-2">
                    <div :class="['w-2 h-2 rounded-full', canPerformSearch ? 'bg-green-500' : 'bg-gray-400']"></div>
                    <span class="text-gray-600">{{ canPerformSearch ? 'READY' : 'WAITING' }}</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div :class="['w-2 h-2 rounded-full', loading ? 'bg-yellow-500 animate-pulse' : 'bg-gray-400']"></div>
                    <span class="text-gray-600">{{ loading ? 'PROCESSING' : 'IDLE' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Panel: Output -->
            <div class="bg-black text-white">
              <!-- Response Header -->
              <div class="p-4 border-b border-gray-700 flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span class="font-mono text-sm">RESPONSE_STREAM</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-gray-400">{{ Object.keys(results).length }} objects</span>
                  <button
                    @click="copyResults"
                    class="text-xs text-gray-400 hover:text-white px-2 py-1 border border-gray-600 hover:border-gray-400 transition-colors"
                  >
                    COPY
                  </button>
                </div>
              </div>

              <!-- Response Content -->
              <div class="p-6 font-mono text-sm overflow-auto min-h-[500px]">
                <!-- Loading Animation -->
                <div v-if="loading" class="space-y-2">
                  <div class="flex items-center space-x-2">
                    <div class="w-1 h-4 bg-white animate-pulse"></div>
                    <span class="text-gray-400">Initializing query...</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-1 h-4 bg-white animate-pulse"></div>
                    <span class="text-gray-400">Processing {{ searchMode }}...</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <div class="w-1 h-4 bg-white animate-pulse"></div>
                    <span class="text-gray-400">Fetching results...</span>
                  </div>
                </div>

                <!-- Error State -->
                <div v-else-if="error" class="space-y-3">
                  <div class="text-red-400">
                    <span class="text-gray-400">$ </span>ERROR: {{ error }}
                  </div>
                  <div class="text-gray-500">
                    <span class="text-gray-400">$ </span>exit_code: 1
                  </div>
                </div>

                <!-- Results -->
                <div v-else-if="hasResults" class="space-y-1">
                  <div class="text-gray-400 mb-4">
                    <span class="text-gray-400">$ </span>query_executed_successfully
                  </div>
                  <pre class="text-green-400 whitespace-pre-wrap">{{ JSON.stringify(results, null, 2) }}</pre>
                </div>

                <!-- Default State -->
                <div v-else class="space-y-3 text-gray-500">
                  <div><span class="text-gray-600">$ </span>rwanda-geo-api --version</div>
                  <div class="text-white">v2.1.0</div>
                  <div><span class="text-gray-600">$ </span>rwanda-geo-api --help</div>
                  <div class="mt-4 space-y-1 text-gray-400">
                    <div>Available endpoints:</div>
                    <div>  → /api/search?searchQuery=&lt;query&gt;</div>
                    <div>  → /api/search?searchQuery=&lt;lat,lng&gt;</div>
                    <div class="mt-3">Parameters:</div>
                    <div>  --lang: en|fr|rw</div>
                    <div>  --filterData: all|province|district|...</div>
                    <div class="mt-3 text-gray-500">Enter your query to begin...</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API Usage Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          <div class="bg-white border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-black">99.9%</div>
            <div class="text-sm text-gray-600 uppercase tracking-wider">Uptime</div>
          </div>
          <div class="bg-white border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-black">&lt;200ms</div>
            <div class="text-sm text-gray-600 uppercase tracking-wider">Avg Response</div>
          </div>
          <div class="bg-white border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-black">1M+</div>
            <div class="text-sm text-gray-600 uppercase tracking-wider">Queries/Month</div>
          </div>
        </div>
      </div>
    </section>

    <!-- cURL Code Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-6xl mx-auto px-6">
        <div class="text-center mb-12">
          <h2 class="text-4xl font-bold text-black mb-4">Implementation</h2>
          <p class="text-xl text-gray-600">Copy, paste, and you're ready to go.</p>
        </div>

        <!-- Code Block -->
        <div class="bg-white border-2 border-black shadow-lg">
          <div class="bg-black px-6 py-4 flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <span class="text-white font-mono text-sm">implementation.sh</span>
            </div>
            <button
              @click="copyCurlCommand"
              class="text-white hover:text-gray-300 text-sm font-bold transition-colors"
            >
              COPY_TO_CLIPBOARD
            </button>
          </div>
          <div class="p-6 bg-gray-900 text-green-400 font-mono text-sm overflow-x-auto">
            <pre>{{ curlCommand }}</pre>
          </div>
        </div>

        <!-- Quick Integration Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
          <div class="bg-white border border-gray-200 p-6 hover:border-black transition-colors duration-200">
            <h3 class="text-lg font-bold text-black mb-3">JavaScript</h3>
            <code class="text-sm text-gray-600 block">
              fetch('/api/search?...')<br/>
              .then(res => res.json())
            </code>
          </div>
          <div class="bg-white border border-gray-200 p-6 hover:border-black transition-colors duration-200">
            <h3 class="text-lg font-bold text-black mb-3">Python</h3>
            <code class="text-sm text-gray-600 block">
              import requests<br/>
              response = requests.get(...)
            </code>
          </div>
          <div class="bg-white border border-gray-200 p-6 hover:border-black transition-colors duration-200">
            <h3 class="text-lg font-bold text-black mb-3">PHP</h3>
            <code class="text-sm text-gray-600 block">
              $response = file_get_contents(...)
              $data = json_decode($response)
            </code>
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Head } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import axios from 'axios';
import { debounce } from 'lodash';

// Reactive state
const searchMode = ref('address');
const searchQuery = ref('');
const latitudeInput = ref('');
const longitudeInput = ref('');
const lang = ref('en');
const selectedFilter = ref('all');
const loading = ref(false);
const error = ref(null);
const results = ref({});
const gettingLocation = ref(false);

// Computed properties
const canPerformSearch = computed(() => {
  if (searchMode.value === 'address') {
    return searchQuery.value.trim().length >= 4;
  } else {
    const lat = parseFloat(latitudeInput.value);
    const lng = parseFloat(longitudeInput.value);
    return !isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
  }
});

const hasResults = computed(() => {
  return Object.values(results.value).some(array => array && Array.isArray(array) && array.length > 0);
});

const curlCommand = computed(() => {
  const queryParam = searchMode.value === 'address'
    ? `searchQuery=${encodeURIComponent(searchQuery.value)}` 
    : `searchQuery=${encodeURIComponent(`${latitudeInput.value},${longitudeInput.value}`)}`;
  return `curl -X GET "https://onrwanda.com/api/search?${queryParam}&lang=${lang.value}&filterData=${selectedFilter.value}" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json"`;
});

// Methods
const search = async () => {
  let currentSearchQuery = '';

  if (searchMode.value === 'address') {
    currentSearchQuery = searchQuery.value.trim();
    if (currentSearchQuery.length < 4) {
      error.value = "Address search query should be at least 4 characters long.";
      results.value = {};
      return;
    }
  } else {
    const lat = parseFloat(latitudeInput.value);
    const lng = parseFloat(longitudeInput.value);
    if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      error.value = "Please enter valid numeric latitude (-90 to 90) and longitude (-180 to 180).";
      results.value = {};
      return;
    }
    currentSearchQuery = `${lat},${lng}`;
  }

  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get('/api/search', {
      params: {
        searchQuery: currentSearchQuery,
        lang: lang.value,
        filterData: selectedFilter.value,
      },
    });

    const processedResults = {};
    for (const type of Object.keys(response.data)) {
      if (Array.isArray(response.data[type])) {
        processedResults[type] = response.data[type].map(item => ({
          ...item,
          latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
          longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
        }));
      } else {
        processedResults[type] = response.data[type];
      }
    }
    results.value = processedResults;
  } catch (e) {
    error.value = e.response?.data?.errors
      ? Object.values(e.response.data.errors).flat().join(', ')
      : e.response?.data?.message || 'An error occurred while fetching results.';
    console.error('Search error:', e);
    results.value = {};
  } finally {
    loading.value = false;
  }
};

const getCurrentLocation = () => {
  if (!('geolocation' in navigator)) {
    error.value = 'Geolocation is not supported by your browser.';
    return;
  }

  gettingLocation.value = true;
  error.value = null;

  navigator.geolocation.getCurrentPosition(
    (position) => {
      latitudeInput.value = position.coords.latitude.toFixed(6);
      longitudeInput.value = position.coords.longitude.toFixed(6);
      searchMode.value = 'coordinates';
      gettingLocation.value = false;
      search();
    },
    (geoError) => {
      gettingLocation.value = false;
      error.value = "Unable to get your location. Please enter coordinates manually.";
    },
    {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0,
    }
  );
};

const copyResults = async () => {
  try {
    await navigator.clipboard.writeText(JSON.stringify(results.value, null, 2));
  } catch (err) {
    console.error('Failed to copy results:', err);
  }
};

const copyCurlCommand = async () => {
  try {
    await navigator.clipboard.writeText(curlCommand.value);
  } catch (err) {
    console.error('Failed to copy cURL command:', err);
  }
};

// Auto-search with debounce
const debouncedSearch = debounce(search, 500);

watch([searchQuery, latitudeInput, longitudeInput, lang, selectedFilter], () => {
  if (canPerformSearch.value) {
    debouncedSearch();
  } else {
    error.value = null;
    results.value = {};
  }
});
</script>

<style scoped>

body {
    font-family: 'Inter', sans-serif;
}

.font-mono {
    font-family: 'Roboto Mono', monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  line-height: 1.5;
}

/* Input focus effects */
input:focus, select:focus {
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

/* Button hover effects */
button {
  position: relative;
  overflow: hidden;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
}

/* Loading animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>