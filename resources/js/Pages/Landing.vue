<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, MapPin, Globe, Building, Home, Hospital, Settings } from 'lucide-vue-next';
import axios from 'axios';
import { debounce } from 'lodash';

// --- Reactive State ---
const searchQuery = ref('');
const selectedLanguage = ref('en');
const selectedFilter = ref('all');
const showFilters = ref(false);
const isLoading = ref(false);
const error = ref(null);
const searchTime = ref(0);

// Animation states
const mousePosition = ref({ x: 0, y: 0 });
const isTyping = ref(false);
const eyeDirection = ref({ x: 0, y: 0 });
const headRotation = ref(0);
const showGreeting = ref(true);
const greetingStage = ref('wave'); // 'wave', 'speak', 'highfive', 'complete'
const armPosition = ref({ left: 0, right: 0 });
const mouthState = ref('smile'); // 'neutral', 'smile', 'speaking'

// Search results structure
const ALL_RESULT_TYPES = ['provinces', 'districts', 'sectors', 'cells', 'villages', 'healthFacs'];
const searchResults = ref(
    Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]))
);

// Feature card data
const features = ref([
    {
        title: "Real-Time Tracking",
        description: "Track assets with high-precision GPS for instant location updates.",
        list: [
            "Low-latency updates",
            "High-precision tracking",
            "Real-time monitoring"
        ],
        image: "/images/tracking.jpg"
    },
    {
        title: "Geofencing Precision",
        description: "Set accurate boundaries using Rwanda's administrative data.",
        list: [
            "Less than 1m accuracy",
            "Complete boundary data",
            "Customizable zones"
        ],
        image: "/images/geofencing.jpg"
    }
]);

// Configuration
const languages = [
    { code: 'rw', name: 'Kinyarwanda' },
    { code: 'en', name: 'English' },
    { code: 'fr', name: 'Français' },
];

const filters = [
    { code: 'all', name: 'All Locations', icon: Globe },
    { code: 'province', name: 'Provinces', icon: Building },
    { code: 'district', name: 'Districts', icon: Building },
    { code: 'sector', name: 'Sectors', icon: Home },
    { code: 'cell', name: 'Cells', icon: Home },
    { code: 'village', name: 'Villages', icon: Home },
    { code: 'health_fac', name: 'Health Facilities', icon: Hospital },
];

// --- Computed Properties ---
const totalResults = computed(() => {
    return ALL_RESULT_TYPES.reduce((sum, type) => sum + (searchResults.value[type]?.length || 0), 0);
});

const hasResults = computed(() => totalResults.value > 0);

const getPlaceholderText = computed(() => ({
    rw: 'Shakisha ahantu mu Rwanda...',
    en: 'Search Rwanda locations...',
    fr: 'Rechercher des lieux au Rwanda...',
}[selectedLanguage.value] || 'Search locations...'));

// --- Animation Functions ---
const startGreetingSequence = () => {
    // Stage 1: Wave (1s)
    greetingStage.value = 'wave';
    armPosition.value = { left: 0, right: -20 };
    
    setTimeout(() => {
        // Stage 2: Speak "Muraho" (2s)
        greetingStage.value = 'speak';
        mouthState.value = 'speaking';
        armPosition.value = { left: 0, right: 0 };
    }, 1000);

    setTimeout(() => {
        // Stage 3: High five preparation (0.5s)
        greetingStage.value = 'highfive';
        armPosition.value = { left: -30, right: -30 };
        mouthState.value = 'smile';
    }, 3000);

    setTimeout(() => {
        // Stage 4: High five impact (0.5s)
        armPosition.value = { left: -45, right: -45 };
    }, 3500);

    setTimeout(() => {
        // Stage 5: Return to normal and hide greeting
        greetingStage.value = 'complete';
        armPosition.value = { left: 0, right: 0 };
        showGreeting.value = false;
        mouthState.value = 'neutral';
    }, 4500);
};

// --- Search Functions ---
const performSearch = debounce(async (query, lang, filter) => {
    if (query.trim().length < 2) {
        clearSearch(false);
        return;
    }

    isLoading.value = true;
    error.value = null;
    const startTime = performance.now();

    try {
        const { data } = await axios.post('/map/search-json', {
            searchQuery: query.trim(),
            lang,
            filterData: filter,
        });

        searchResults.value = Object.fromEntries(
            ALL_RESULT_TYPES.map(type => {
                const items = data[type] || [];
                return [
                    type,
                    items.map(item => ({
                        ...item,
                        geojson: typeof item.geojson === 'string' ? JSON.parse(item.geojson) : item.geojson,
                        latitude: typeof item.latitude === 'string' ? parseFloat(item.latitude) : item.latitude,
                        longitude: typeof item.longitude === 'string' ? parseFloat(item.longitude) : item.longitude,
                    }))
                ];
            })
        );
    } catch (err) {
        console.error('Search error:', err);
        error.value = err.response?.data?.message || 'Failed to fetch search results.';
        clearSearch(false);
    } finally {
        isLoading.value = false;
        searchTime.value = Math.round(performance.now() - startTime);
    }
}, 300);

const clearSearch = (resetQuery = true) => {
    if (resetQuery) {
        searchQuery.value = '';
    }
    searchResults.value = Object.fromEntries(ALL_RESULT_TYPES.map(type => [type, []]));
    error.value = null;
    isLoading.value = false;
    searchTime.value = 0;
};

const getDisplayName = (result) => {
    return result[`name_${selectedLanguage.value}`] || result.name_en || result.name_local || result.name || 'N/A';
};

// Mouse tracking functions
const updateMousePosition = (event) => {
    if (showGreeting.value) return; // Don't track mouse during greeting
    
    mousePosition.value = { x: event.clientX, y: event.clientY };

    // Calculate eye direction based on mouse position
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const deltaX = (event.clientX - centerX) / centerX;
    const deltaY = (event.clientY - centerY) / centerY;

    eyeDirection.value = {
        x: Math.max(-1, Math.min(1, deltaX * 0.4)),
        y: Math.max(-1, Math.min(1, deltaY * 0.4))
    };

    // Calculate head rotation (more subtle)
    headRotation.value = deltaX * 3;
};

const handleTyping = () => {
    if (showGreeting.value) return;
    
    isTyping.value = true;
    mouthState.value = 'smile';
    
    setTimeout(() => {
        isTyping.value = false;
        mouthState.value = 'neutral';
    }, 1500);
};

// --- Lifecycle ---
onMounted(() => {
    document.addEventListener('mousemove', updateMousePosition);
    // Start greeting sequence after component mounts
    setTimeout(startGreetingSequence, 500);
});

onUnmounted(() => {
    document.removeEventListener('mousemove', updateMousePosition);
});

// --- Watchers ---
watch(searchQuery, (newQuery) => {
    handleTyping();
    performSearch(newQuery, selectedLanguage.value, selectedFilter.value);
});

watch(selectedLanguage, (newLang) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, newLang, selectedFilter.value);
    }
});

watch(selectedFilter, (newFilter) => {
    if (searchQuery.value.trim()) {
        performSearch(searchQuery.value, selectedLanguage.value, newFilter);
    }
});
</script>

<template>
    <Head title="Rwanda Geo - Professional Geocoding Platform" />
    <AppLayout>
        <!-- Main Container with subtle pattern background -->
        <div class="min-h-screen bg-white relative overflow-hidden">
            <!-- Subtle geometric background pattern -->
            <div class="absolute inset-0 opacity-[0.02]">
                <svg class="w-full h-full" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
                            <path d="M 60 0 L 0 0 0 60" fill="none" stroke="#000" stroke-width="1"/>
                            <circle cx="30" cy="30" r="2" fill="#000"/>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
            </div>

            <!-- Hero Section -->
            <section class="min-h-screen relative z-10 py-20 px-4 sm:px-6 lg:px-8">
                <div class="max-w-4xl mx-auto h-full">
                    <div class="flex flex-col items-center justify-center space-y-16 min-h-[80vh]">

                        <!-- Interactive Character with Greeting -->
                        <div class="flex justify-center relative">
                            <!-- Greeting Text - Muraho -->
                            <div 
                                v-if="showGreeting"
                                class="absolute -top-24 left-1/2 transform -translate-x-1/2 z-20"
                                :class="{
                                    'animate-bounce': greetingStage === 'wave',
                                    'animate-pulse': greetingStage === 'speak',
                                    'animate-ping': greetingStage === 'highfive'
                                }"
                            >
                                <div class="bg-white border-3 border-black rounded-3xl px-8 py-4 shadow-xl relative">
                                    <p class="text-3xl font-bold text-black tracking-wide">Muraho!</p>
                                    <!-- Speech bubble tail -->
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[12px] border-r-[12px] border-t-[12px] border-l-transparent border-r-transparent border-t-black"></div>
                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 translate-y-[-3px] w-0 h-0 border-l-[9px] border-r-[9px] border-t-[9px] border-l-transparent border-r-transparent border-t-white"></div>
                                </div>
                            </div>

                            <!-- Advanced Character Container -->
                            <div class="character-container relative">
                                <svg
                                    class="w-64 h-64 text-black transition-all duration-700 ease-out drop-shadow-lg"
                                    :style="{ 
                                        transform: `rotate(${headRotation}deg) scale(${showGreeting ? 1.1 : 1})`,
                                        filter: isTyping ? 'drop-shadow(0 0 8px rgba(0,0,0,0.3))' : 'drop-shadow(0 4px 12px rgba(0,0,0,0.15))'
                                    }"
                                    viewBox="0 0 140 140"
                                    fill="none"
                                >
                                    <!-- Outer Head Glow -->
                                    <ellipse 
                                        cx="70" 
                                        cy="65" 
                                        rx="38" 
                                        ry="35" 
                                        stroke="currentColor" 
                                        stroke-width="1" 
                                        fill="none" 
                                        opacity="0.2"
                                    />
                                    
                                    <!-- Main Head Shape -->
                                    <ellipse 
                                        cx="70" 
                                        cy="65" 
                                        rx="36" 
                                        ry="33" 
                                        stroke="currentColor" 
                                        stroke-width="3" 
                                        fill="white"
                                        class="transition-all duration-500"
                                    />
                                    
                                    <!-- Forehead Contours -->
                                    <path 
                                        d="M 40 50 Q 55 45 70 45 Q 85 45 100 50" 
                                        stroke="currentColor" 
                                        stroke-width="2" 
                                        fill="none"
                                        opacity="0.6"
                                    />
                                    <path 
                                        d="M 45 55 Q 60 52 70 52 Q 80 52 95 55" 
                                        stroke="currentColor" 
                                        stroke-width="1.5" 
                                        fill="none"
                                        opacity="0.4"
                                    />

                                    <!-- Left Eye Assembly -->
                                    <g class="left-eye">
                                        <!-- Eye Socket Shadow -->
                                        <ellipse cx="56" cy="58" rx="10" ry="9" fill="rgba(0,0,0,0.05)"/>
                                        <!-- Eye Socket -->
                                        <ellipse cx="56" cy="57" rx="9" ry="8" stroke="currentColor" stroke-width="2.5" fill="white"/>
                                        <!-- Eye White -->
                                        <ellipse cx="56" cy="57" rx="7" ry="6" fill="white"/>
                                        <!-- Iris -->
                                        <circle
                                            :cx="56 + eyeDirection.x * 2.5"
                                            :cy="57 + eyeDirection.y * 2"
                                            r="4"
                                            fill="rgba(0,0,0,0.1)"
                                            class="transition-all duration-200 ease-out"
                                        />
                                        <!-- Pupil -->
                                        <circle
                                            :cx="56 + eyeDirection.x * 2.5"
                                            :cy="57 + eyeDirection.y * 2"
                                            r="2.5"
                                            fill="currentColor"
                                            class="transition-all duration-200 ease-out"
                                        />
                                        <!-- Eye Shine -->
                                        <circle
                                            :cx="55 + eyeDirection.x * 2.5"
                                            :cy="56 + eyeDirection.y * 2"
                                            r="1"
                                            fill="white"
                                            class="transition-all duration-200 ease-out"
                                        />
                                    </g>

                                    <!-- Right Eye Assembly -->
                                    <g class="right-eye">
                                        <!-- Eye Socket Shadow -->
                                        <ellipse cx="84" cy="58" rx="10" ry="9" fill="rgba(0,0,0,0.05)"/>
                                        <!-- Eye Socket -->
                                        <ellipse cx="84" cy="57" rx="9" ry="8" stroke="currentColor" stroke-width="2.5" fill="white"/>
                                        <!-- Eye White -->
                                        <ellipse cx="84" cy="57" rx="7" ry="6" fill="white"/>
                                        <!-- Iris -->
                                        <circle
                                            :cx="84 + eyeDirection.x * 2.5"
                                            :cy="57 + eyeDirection.y * 2"
                                            r="4"
                                            fill="rgba(0,0,0,0.1)"
                                            class="transition-all duration-200 ease-out"
                                        />
                                        <!-- Pupil -->
                                        <circle
                                            :cx="84 + eyeDirection.x * 2.5"
                                            :cy="57 + eyeDirection.y * 2"
                                            r="2.5"
                                            fill="currentColor"
                                            class="transition-all duration-200 ease-out"
                                        />
                                        <!-- Eye Shine -->
                                        <circle
                                            :cx="83 + eyeDirection.x * 2.5"
                                            :cy="56 + eyeDirection.y * 2"
                                            r="1"
                                            fill="white"
                                            class="transition-all duration-200 ease-out"
                                        />
                                    </g>

                                    <!-- Nose Area -->
                                    <ellipse cx="70" cy="70" rx="4" ry="3" stroke="currentColor" stroke-width="1.5" fill="none" opacity="0.7"/>
                                    
                                    <!-- Nostrils -->
                                    <ellipse cx="68" cy="71" rx="1" ry="1.5" fill="currentColor" opacity="0.8"/>
                                    <ellipse cx="72" cy="71" rx="1" ry="1.5" fill="currentColor" opacity="0.8"/>

                                    <!-- Dynamic Mouth States -->
                                    <g class="mouth-area">
                                        <!-- Mouth Base -->
                                        <ellipse 
                                            cx="70" 
                                            cy="82" 
                                            rx="12" 
                                            ry="4" 
                                            stroke="currentColor" 
                                            stroke-width="1" 
                                            fill="rgba(0,0,0,0.02)" 
                                            opacity="0.5"
                                        />
                                        
                                        <!-- Neutral Mouth -->
                                        <path
                                            v-if="mouthState === 'neutral'"
                                            d="M 62 82 Q 70 83 78 82"
                                            stroke="currentColor"
                                            stroke-width="2.5"
                                            fill="none"
                                            class="transition-all duration-300"
                                        />
                                        
                                        <!-- Speaking/Typing Mouth -->
                                        <ellipse
                                            v-else-if="mouthState === 'typing' || mouthState === 'speaking'"
                                            cx="70"
                                            cy="82"
                                            :rx="mouthState === 'typing' ? 4 : 6"
                                            :ry="mouthState === 'typing' ? 3 : 4"
                                            stroke="currentColor"
                                            stroke-width="2.5"
                                            fill="rgba(0,0,0,0.1)"
                                            class="transition-all duration-200"
                                        />
                                        
                                        <!-- Pause Mouth (during typing) -->
                                        <path
                                            v-else-if="mouthState === 'pause'"
                                            d="M 65 82 Q 70 80 75 82"
                                            stroke="currentColor"
                                            stroke-width="2.5"
                                            fill="none"
                                            class="transition-all duration-200"
                                        />
                                        
                                        <!-- Smile -->
                                        <path
                                            v-else-if="mouthState === 'smile'"
                                            d="M 60 82 Q 70 88 80 82"
                                            stroke="currentColor"
                                            stroke-width="2.5"
                                            fill="none"
                                            class="transition-all duration-300"
                                        />
                                    </g>

                                    <!-- Chin Definition -->
                                    <path 
                                        d="M 50 85 Q 70 90 90 85" 
                                        stroke="currentColor" 
                                        stroke-width="1.5" 
                                        fill="none"
                                        opacity="0.3"
                                    />
                                </svg>

                                <!-- Interactive Particles -->
                                <div 
                                    v-if="isTyping"
                                    class="absolute inset-0 pointer-events-none"
                                >
                                    <div class="absolute top-8 right-8 animate-ping">
                                        <div class="w-2 h-2 bg-black rounded-full opacity-20"></div>
                                    </div>
                                    <div class="absolute bottom-12 left-8 animate-pulse">
                                        <div class="w-1.5 h-1.5 bg-black rounded-full opacity-30"></div>
                                    </div>
                                    <div class="absolute top-16 left-12 animate-bounce">
                                        <div class="w-1 h-1 bg-black rounded-full opacity-25"></div>
                                    </div>
                                </div>

                                <!-- High-five Effect -->
                                <div 
                                    v-if="greetingStage === 'highfive'"
                                    class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                                >
                                    <div class="animate-ping">
                                        <div class="w-32 h-32 border-4 border-black rounded-full opacity-10"></div>
                                    </div>
                                    <div class="animate-pulse">
                                        <div class="w-24 h-24 border-2 border-black rounded-full opacity-20"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Floating Map Elements -->
                            <div 
                                class="absolute -top-8 -right-8 transition-all duration-700"
                                :class="{ 'animate-float': !showGreeting }"
                            >
                                <MapPin class="w-7 h-7 text-black drop-shadow-md" />
                            </div>
                            <div 
                                class="absolute -bottom-4 -left-8 transition-all duration-500"
                                :class="{ 'animate-pulse': !showGreeting }"
                            >
                                <Globe class="w-6 h-6 text-gray-600 drop-shadow-sm" />
                            </div>
                        </div>

                        <!-- Main Heading -->
                        <div class="text-center relative z-10">
                            <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold text-black tracking-tight">
                                Rwanda Map Platform
                            </h1>
                            <p class="text-xl md:text-2xl text-gray-600 mt-4 font-light">
                                Professional geocoding for Rwanda
                            </p>
                        </div>

                        <!-- Search Card -->
                        <div class="flex justify-center w-full relative z-10">
                            <Card class="border-2 border-black rounded-3xl bg-white w-full max-w-lg shadow-lg">
                                <CardHeader class="pb-4">
                                    <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
                                        <CardTitle class="text-xl font-bold text-black">Search Rwanda</CardTitle>
                                        <button
                                            @click="showFilters = !showFilters"
                                            class="border border-black text-black hover:bg-black hover:text-white rounded-full px-3 py-1 text-sm font-medium flex items-center gap-2 transition-all duration-200"
                                        >
                                            <Settings class="w-4 h-4" />
                                            Filters
                                        </button>
                                    </div>
                                </CardHeader>
                                <CardContent class="space-y-4">
                                    <!-- Search Input -->
                                    <div class="relative">
                                        <Input
                                            v-model="searchQuery"
                                            :placeholder="getPlaceholderText"
                                            class="h-14 pl-12 pr-4 text-lg border-2 border-gray-200 rounded-2xl focus:border-black focus:ring-0 transition-all duration-200"
                                        />
                                        <Search class="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
                                    </div>

                                    <!-- Filters Panel -->
                                    <div v-if="showFilters" class="space-y-4 p-4 bg-gray-50 rounded-2xl border-2 border-gray-200">
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <!-- Language Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                                                <Select v-model="selectedLanguage">
                                                    <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                        <SelectValue placeholder="Select language" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="lang in languages" :key="lang.code" :value="lang.code">
                                                            {{ lang.name }}
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <!-- Location Type Filter -->
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Location Type</label>
                                                <Select v-model="selectedFilter">
                                                    <SelectTrigger class="h-12 border-2 border-gray-200 rounded-2xl">
                                                        <SelectValue placeholder="Select type" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem v-for="filter in filters" :key="filter.code" :value="filter.code">
                                                            <div class="flex items-center gap-2">
                                                                <component :is="filter.icon" class="w-4 h-4" />
                                                                {{ filter.name }}
                                                            </div>
                                                        </SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Search Results -->
                                    <div v-if="isLoading || hasResults || error" class="space-y-3">
                                        <!-- Loading State -->
                                        <div v-if="isLoading" class="flex items-center justify-center py-4">
                                            <div class="flex items-center space-x-2">
                                                <div class="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                                                <span class="text-gray-600 text-sm">Searching...</span>
                                            </div>
                                        </div>

                                        <!-- Error State -->
                                        <div v-if="error" class="p-3 bg-red-50 border border-red-200 rounded-xl">
                                            <p class="text-red-700 text-sm">{{ error }}</p>
                                        </div>

                                        <!-- Results -->
                                        <div v-if="hasResults && !isLoading" class="space-y-2">
                                            <p class="text-xs text-gray-500 text-center">
                                                {{ totalResults }} location{{ totalResults !== 1 ? 's' : '' }} found
                                            </p>

                                            <div class="max-h-48 overflow-y-auto space-y-2">
                                                <template v-for="(type, typeKey) in searchResults" :key="typeKey">
                                                    <div v-if="type.length > 0">
                                                        <div v-for="result in type.slice(0, 2)" :key="result.id"
                                                             class="p-3 bg-gray-50 border border-gray-200 rounded-xl hover:border-black transition-colors cursor-pointer">
                                                            <div class="space-y-1">
                                                                <p class="font-medium text-black text-sm">{{ getDisplayName(result) }}</p>
                                                                <p v-if="result.address" class="text-xs text-gray-600">
                                                                    {{ result.address }}
                                                                </p>
                                                                <p class="text-xs text-gray-500">
                                                                    {{ result.latitude?.toFixed(4) }}, {{ result.longitude?.toFixed(4) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </AppLayout>
</template>

<style scoped>
/* Custom animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: transform, opacity, color, background-color, border-color;
    transition-timing-function: ease-out;
}
</style>



<!--
 
^*4TbA{yVe&N  for <EMAIL>
B1*T9JCZtc{? for <EMAIL>
g*Sq7K3ZrY4<NAME_EMAIL>	
{Ro2muy0%%<NAME_EMAIL>
S9ewYw[<NAME_EMAIL>
-->