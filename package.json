{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build && vite build --ssr", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/server-renderer": "^3.5.18", "autoprefixer": "^10.4.16", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^6.2.4", "vue": "^3.3.13"}, "dependencies": {"@vueuse/core": "^13.8.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.525.0", "maplibre-gl": "^5.6.1", "reka-ui": "^2.4.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}}