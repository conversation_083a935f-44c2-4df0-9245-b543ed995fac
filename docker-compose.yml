services:
    laravel.test:
        build:
            context: .
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: sail-8.4/app
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - mysql
            - typesense
            - redis
            - osrm
            - nominatim
    mysql:
        image: 'mysql/mysql-server:8.0'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: '%'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - 'sail-mysql:/var/lib/mysql'
            - './vendor/laravel/sail/database/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - mysqladmin
                - ping
                - '-p${DB_PASSWORD}'
            retries: 3
            timeout: 5s
    typesense:
        image: 'typesense/typesense:29.0'
        ports:
            - '${FORWARD_TYPESENSE_PORT:-8108}:8108'
        environment:
            TYPESENSE_DATA_DIR: '${TYPESENSE_DATA_DIR:-/typesense-data}'
            TYPESENSE_API_KEY: '${TYPESENSE_API_KEY:-xyz}'
            TYPESENSE_ENABLE_CORS: '${TYPESENSE_ENABLE_CORS:-true}'
        volumes:
            - 'sail-typesense:/typesense-data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - bash
                - '-c'
                - 'exec 3<>/dev/tcp/localhost/8108 && printf ''GET /health HTTP/1.1\r\nConnection: close\r\n\r\n'' >&3 && head -n1 <&3 | grep ''200'' && exec 3>&-'
            retries: 5
            timeout: 7s
    redis:
        image: 'redis:alpine'
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'sail-redis:/data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    osrm-preprocess:
        image: 'ghcr.io/project-osrm/osrm-backend:latest'
        volumes:
             - './dataset-osm:/data'
        command: >
         sh -c "
          if [ ! -f /data/rwanda-latest.osrm ]; then
              osrm-extract -p /opt/car.lua /data/rwanda-latest.osm.pbf || echo 'osrm-extract failed';
              osrm-partition /data/rwanda-latest.osrm || echo 'osrm-partition failed';
              osrm-customize /data/rwanda-latest.osrm || echo 'osrm-customize failed';
         else
               echo 'OSRM files already exist, skipping preprocessing';
         fi
         "
        networks:
            - sail
    osrm:
        image: 'ghcr.io/project-osrm/osrm-backend:latest'
        ports:
            - '${FORWARD_OSRM_PORT:-5000}:5000'
        volumes:
            - './dataset-osm:/data'
        command: >
            sh -c "osrm-routed --algorithm mld /data/rwanda-latest.osrm"
        networks:
            - sail
        depends_on:
            - osrm-preprocess
        healthcheck:
            test:
                - CMD
                - curl
                - --fail
                - 'http://localhost:5000/route/v1/driving/0,0;1,1?steps=true'
            interval: 30s
            timeout: 10s
            retries: 3
    nominatim:
        image: 'mediagis/nominatim:5.1'
        ports:
            - '${FORWARD_NOMINATIM_PORT:-6900}:8080'
        environment:
            PBF_URL: 'https://download.geofabrik.de/africa/rwanda-latest.osm.pbf'
            REPLICATION_URL: 'https://download.geofabrik.de/africa/rwanda-updates/'
            NOMINATIM_PASSWORD: 'very_secure_password'
            IMPORT_WIKIPEDIA: 'false'
            IMPORT_STYLE: 'full'
            THREADS: '4'
            POSTGRES_SHARED_BUFFERS: '1GB'
            POSTGRES_MAINTENANCE_WORK_MEM: '2GB'
            POSTGRES_WORK_MEM: '50MB'
            POSTGRES_EFFECTIVE_CACHE_SIZE: '3GB'
            REPLICATION_UPDATE_INTERVAL: '86400'
            REPLICATION_RECHECK_INTERVAL: '900'
            UPDATE_MODE: 'continuous'
        volumes:
            - 'sail-nominatim-data:/var/lib/postgresql/16/main'
            - 'sail-nominatim-flatnode:/nominatim/flatnode'
            - './dataset-osm:/nominatim/data'
        shm_size: '1g'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - curl
                - --fail
                - 'http://localhost:8080/status.php'
            interval: 30s
            timeout: 10s
            retries: 3
networks:
    sail:
        driver: bridge
volumes:
    sail-mysql:
        driver: local
    sail-typesense:
        driver: local
    sail-redis:
        driver: local
    sail-osrm:
        driver: local
    sail-nominatim-data:
        driver: local
    sail-nominatim-flatnode:
        driver: local