<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NavigationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
           'from' => 'required|array',
           'from.latitude' => 'required|numeric|min:-90|max:90',
           'from.longitude' => 'required|numeric|min:-180|max:180',
           'to' => 'required|array',
           'to.latitude' => 'required|numeric|min:-90|max:90',
           'to.longitude' => 'required|numeric|min:-180|max:180',
           'throught' => 'nullable|array',
           'throught.*.latitude' => 'required|numeric|min:-90|max:90',
           'throught.*.longitude' => 'required|numeric|min:-180|max:180',
           'through.*.index' => 'required|numeric|min:0',
           'mode' => 'required|in:car,bike,walk',
           'language' => 'required|in:fr,en,rw',
        ];
    }
}
