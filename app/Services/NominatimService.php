<?php

namespace App\Services;

use App\Data\SearchLatLongData;
use Illuminate\Support\Facades\Http;

class NominatimService
{
    public function __construct(protected MapService $mapService) {}

    public function search(string $searchQuery): array
    {
        $port = env('FORWARD_NOMINATIM_PORTx', 8080);

        $result = Http::get("http://nominatim:$port/search?q=$searchQuery&format=json")->json();

        // Loop through each result, get address from mapService, and rebuild
        $formattedResults = array_map(function ($item) {
          //  $data = SearchLatLongData::from($item['lat'], $item['lon']);
          $data = SearchLatLongData::from([
            'latitude' => $item['lat'],
            'longitude' => $item['lon']
          ]);
            $mapResult = $this->mapService->searchLatitudeLongitude($data);
            $address = $mapResult[0]['address'] ?? $item['display_name'] ?? '';

            return [
                'latitude' => $item['lat'],
                'longitude' => $item['lon'],
                'address' => $address,
                'name' => $item['name'] ?? '',
                'type' => $item['type'] ?? '',
            ];
        }, $result);

        return $formattedResults;
    }

    public function reverseSearch(string $latitude, string $longitude): string
    {
        $lat = $latitude;
        $long = $longitude;

        $data = SearchLatLongData::from($lat, $long);

        $result = $this->mapService->searchLatitudeLongitude($data);

        // Return the address from the first result, or empty string if no results
        return $result[0]['address'] ?? '';
    }
}