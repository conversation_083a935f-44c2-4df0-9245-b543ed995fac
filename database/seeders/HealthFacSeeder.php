<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;
use App\Models\HealthFac;
use App\Models\Village;

class HealthFacSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $geojsonPath = storage_path('app/Health_facilities_hrw.geojson');
        if (!File::exists($geojsonPath)) {
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojson = json_decode(File::get($geojsonPath), true);

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            throw new \Exception('No features found in GeoJSON file');
        }

        $batchSize = 500;
        $healthFacilities = [];

        foreach ($geojson['features'] as $feature) {
        
            if ($feature['properties']['cell'] === 'NYAMATA VILLE') {
                $feature['properties']['cell'] = 'Nyamata y\' Umujyi';
            }

            $feature['properties']['cell'] = ucfirst(strtolower($feature['properties']['cell']));
            $feature['properties']['village'] = ucfirst(strtolower($feature['properties']['village']));

            if ($feature['properties']['village'] === 'Rwakibilizi i') {
                $feature['properties']['village'] = 'Rwakibirizi I';
            }

            if ($feature['properties']['village'] === 'Intaramirwa') {
                $feature['properties']['village'] = 'Indangamirwa';
            }

            if ($feature['properties']['cell'] === 'Urugarama' && $feature['properties']['village'] === 'Kabeza') {
                $feature['properties']['village'] = 'Akabeza';
            }

             if ($feature['properties']['cell'] === 'Cyimo' && $feature['properties']['village'] === 'Mwambi') {
                $feature['properties']['village'] = 'Murambi';
            }

            $village = $this->findVillageWithSimilarityMatching($feature['properties']);

            if (!$village) {
                Log::error('Failed to find Village for Health Facility after similarity matching, skipping', [
                    'health_facility_name' => $feature['properties']['name'] ?? 'Unknown',
                    'village_name' => $feature['properties']['village'] ?? 'Unknown',
                    'cell_name' => $feature['properties']['cell'] ?? 'Unknown',
                    'sector_name' => $feature['properties']['sector'] ?? 'Unknown',
                ]);
                continue; 
            }

            $healthFacilities[] = [
                'name' => $feature['properties']['name'] ?? null,
                'type' => $feature['properties']['type'] ?? null,
                'code' => $feature['id'] ?? null,
                'capture_year' => $feature['properties']['capture'] ?? null,
                'layer' => $feature['properties']['layer'] ?? null,
                'source' => $feature['properties']['source'] ?? null,
                'geojson' => json_encode($feature['geometry']),
                'latitude' => $feature['properties']['lat'] ?? null,
                'longitude' => $feature['properties']['long'] ?? null,
                'population' => $feature['properties']['population'] ?? null,
                'description' => $feature['properties']['comments'] ?? null,
                'village_id' => $village->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if (count($healthFacilities) >= $batchSize) {
                HealthFac::insert($healthFacilities);
                $healthFacilities = []; 
            }
        }

        if (!empty($healthFacilities)) {
            HealthFac::insert($healthFacilities);
        }
    }

    private function findVillageWithSimilarityMatching($properties)
    {
        $targetCellName = $properties['cell'];
        $targetVillageName = $properties['village'];
        $sectorName = $properties['sector'];

        $village = Village::where('name', $targetVillageName)
            ->whereHas('cell', function ($query) use ($targetCellName) {
                $query->where('name', $targetCellName);
            })->first();

        if ($village) {
            return $village;
        }

        $allVillages = Village::with('cell:id,name')->select('id', 'name', 'cell_id')->get(); // 
        
        $bestMatch = null;
        $bestSimilarity = 0;
        $minSimilarityThreshold = 0.7;

        foreach ($allVillages as $village) {
            $cellSimilarity = $this->calculateSimilarity($targetCellName, $village->cell->name);
            $villageSimilarity = $this->calculateSimilarity($targetVillageName, $village->name);
            
            $combinedSimilarity = ($cellSimilarity * 0.6) + ($villageSimilarity * 0.4);
            
            if ($combinedSimilarity > $bestSimilarity && $combinedSimilarity >= $minSimilarityThreshold) {
                $bestSimilarity = $combinedSimilarity;
                $bestMatch = $village;
            }
        }

        if ($bestMatch) {
            Log::info('Found village using similarity matching', [
                'health_facility_name' => $properties['name'] ?? 'Unknown',
                'target_village_name' => $targetVillageName,
                'matched_village_name' => $bestMatch->name,
                'target_cell_name' => $targetCellName,
                'matched_cell_name' => $bestMatch->cell->name,
                'similarity_score' => round($bestSimilarity, 3),
                'cell_similarity' => round($this->calculateSimilarity($targetCellName, $bestMatch->cell->name), 3),
                'village_similarity' => round($this->calculateSimilarity($targetVillageName, $bestMatch->name), 3),
                'sector_name' => $sectorName,
            ]);
            return $bestMatch;
        }

        $lowerThreshold = 0.6;
        $bestMatch = null;
        $bestSimilarity = 0;

        foreach ($allVillages as $village) {
            $cellSimilarity = $this->calculateSimilarity($targetCellName, $village->cell->name);
            $villageSimilarity = $this->calculateSimilarity($targetVillageName, $village->name);
            $combinedSimilarity = ($cellSimilarity * 0.6) + ($villageSimilarity * 0.4);
            
            if ($combinedSimilarity > $bestSimilarity && $combinedSimilarity >= $lowerThreshold) {
                $bestSimilarity = $combinedSimilarity;
                $bestMatch = $village;
            }
        }

        if ($bestMatch) {
            Log::warning('Found village using lower similarity threshold', [
                'health_facility_name' => $properties['name'] ?? 'Unknown',
                'target_village_name' => $targetVillageName,
                'matched_village_name' => $bestMatch->name,
                'target_cell_name' => $targetCellName,
                'matched_cell_name' => $bestMatch->cell->name,
                'similarity_score' => round($bestSimilarity, 3),
                'cell_similarity' => round($this->calculateSimilarity($targetCellName, $bestMatch->cell->name), 3),
                'village_similarity' => round($this->calculateSimilarity($targetVillageName, $bestMatch->name), 3),
                'sector_name' => $sectorName,
                'note' => 'Using lower threshold - please verify this match',
            ]);
            return $bestMatch;
        }

        return null;
    }

    private function calculateSimilarity($string1, $string2)
    {

        $str1 = strtolower(trim($string1));
        $str2 = strtolower(trim($string2));

        if ($str1 === $str2) {
            return 1.0;
        }

        $levenshteinSimilarity = $this->levenshteinSimilarity($str1, $str2);
        
        $jaroWinklerSimilarity = $this->jaroWinklerSimilarity($str1, $str2);
        
        $substringSimilarity = $this->substringSimilarity($str1, $str2);

        return ($levenshteinSimilarity * 0.4) + ($jaroWinklerSimilarity * 0.4) + ($substringSimilarity * 0.2);
    }

    private function levenshteinSimilarity($str1, $str2)
    {
        $maxLength = max(strlen($str1), strlen($str2));
        if ($maxLength === 0) return 1.0;
        
        $distance = levenshtein($str1, $str2);
        return 1 - ($distance / $maxLength);
    }

    private function jaroWinklerSimilarity($str1, $str2)
    {
        $jaroSimilarity = $this->jaroSimilarity($str1, $str2);
        
        $prefixLength = 0;
        $maxPrefix = min(4, min(strlen($str1), strlen($str2)));
        
        for ($i = 0; $i < $maxPrefix; $i++) {
            if ($str1[$i] === $str2[$i]) {
                $prefixLength++;
            } else {
                break;
            }
        }
        
        return $jaroSimilarity + (0.1 * $prefixLength * (1 - $jaroSimilarity));
    }


    private function jaroSimilarity($str1, $str2)
    {
        $len1 = strlen($str1);
        $len2 = strlen($str2);
        
        if ($len1 === 0 && $len2 === 0) return 1.0;
        if ($len1 === 0 || $len2 === 0) return 0.0;
        
        $matchWindow = max(0, intval(max($len1, $len2) / 2) - 1);
        $str1Matches = array_fill(0, $len1, false);
        $str2Matches = array_fill(0, $len2, false);
        
        $matches = 0;
        $transpositions = 0;
        
        for ($i = 0; $i < $len1; $i++) {
            $start = max(0, $i - $matchWindow);
            $end = min($i + $matchWindow + 1, $len2);
            
            for ($j = intval($start); $j < intval($end); $j++) {
                if ($str2Matches[$j] || $str1[$i] !== $str2[$j]) continue;
                
                $str1Matches[$i] = true;
                $str2Matches[$j] = true;
                $matches++;
                break;
            }
        }
        
        if ($matches === 0) return 0.0;
        
        $k = 0;
        for ($i = 0; $i < $len1; $i++) {
            if (!$str1Matches[$i]) continue;
            
            while ($k < $len2 && !$str2Matches[$k]) $k++;
            
            if ($k < $len2 && $str1[$i] !== $str2[$k]) $transpositions++;
            $k++;
        }
        
        return ($matches / $len1 + $matches / $len2 + ($matches - $transpositions / 2) / $matches) / 3;
    }

    private function substringSimilarity($str1, $str2)
    {
        $longer = strlen($str1) > strlen($str2) ? $str1 : $str2;
        $shorter = strlen($str1) > strlen($str2) ? $str2 : $str1;
        
        if (strpos($longer, $shorter) !== false) {
            return strlen($shorter) / strlen($longer);
        }
        
        if (strpos($shorter, $longer) !== false) {
            return strlen($longer) / strlen($shorter);
        }
        
        $maxLen = min(strlen($str1), strlen($str2));
        $commonSuffix = 0;
        
        for ($i = 1; $i <= $maxLen; $i++) {
            if (substr($str1, -$i) === substr($str2, -$i)) {
                $commonSuffix = $i;
            } else {
                break;
            }
        }
        
        return $commonSuffix / max(strlen($str1), strlen($str2));
    }
    
}