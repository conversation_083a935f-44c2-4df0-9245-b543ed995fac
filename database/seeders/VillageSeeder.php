<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use App\Models\Village;
use App\Models\Cell;
use Illuminate\Support\Facades\Log;
use Spinen\Geometry\Geometry;
use Illuminate\Support\Facades\DB;

class VillageSeeder extends Seeder
{
    protected $geometry;

    /**
     * Constructor to inject Geometry service
     *
     * @param Geometry $geometry
     */
    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $geojsonPath = storage_path('app/Village_level_boundary.geojson');
        if (!File::exists($geojsonPath)) {
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojson = json_decode(File::get($geojsonPath), true);

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            throw new \Exception('No features found in GeoJSON file');
        }

        $batchSize = 1000;
        $villages = [];

        foreach ($geojson['features'] as $feature) {
            // Fix specific data errors
            if ($feature['properties']['NAME_4'] === 'Bugarula') {
                $feature['properties']['NAME_4'] = 'Bugarura';
            }

            if ($feature['properties']['NAME_4'] === 'Karama' && $feature['properties']['NAME_3'] === 'Rudashya') {
                $feature['properties']['NAME_3'] = 'Ruhashya';
            }

            // Case-insensitive search for cell
            $cell = Cell::whereRaw('LOWER(name) = ?', [strtolower($feature['properties']['NAME_4'])])
                ->whereHas('sector', function ($query) use ($feature) {
                    $query->whereRaw('LOWER(name) = ?', [strtolower($feature['properties']['NAME_3'])])->select(['id', 'name']);
                })->select(['id', 'name', 'sector_id'])->first();

            if (!$cell) {
                Log::error('Failed to find Cell for Village, skipping', [
                    'village_name' => $feature['properties']['NAME_5'] ?? 'Unknown',
                    'cell_name' => $feature['properties']['NAME_4'] ?? 'Unknown',
                    'sector_name' => $feature['properties']['NAME_3'] ?? 'Unknown',
                ]);
                continue; // Skip to the next feature
            }

            try {
                // Validate geometry
                if (!isset($feature['geometry']) || !in_array($feature['geometry']['type'], ['Polygon', 'MultiPolygon'])) {
                    Log::warning('Invalid or missing geometry for village: ' . ($feature['properties']['NAME_5'] ?? 'Unknown'));
                    continue;
                }

                // Calculate centroid
                $geometryObject = $this->geometry->parseGeoJson(json_encode($feature['geometry']));
                $geometryWkt = $geometryObject->toWkt();
                $centroidObject = $geometryObject->centroid();
                $longitude = $centroidObject->x();
                $latitude = $centroidObject->y();
                $centroidWkt = "POINT($longitude $latitude)";


                // Prepare village data for batch insert
                $villages[] = [
                    'name' => $feature['properties']['NAME_5'] ?? null,
                    'code' => $feature['properties']['code'] ?? null,
                    'capture_year' => $feature['properties']['capture'] ?? null,
                    'source' => $feature['properties']['source'] ?? null,
                    'geojson' => json_encode($feature['geometry']),
                    'shape_length' => $feature['properties']['shape_Length'] ?? null,
                    'shape_area' => $feature['properties']['shape_Area'] ?? null,
                    'population' => $feature['properties']['population'] ?? null,
                    'description' => $feature['properties']['description'] ?? null,
                    'cell_id' => $cell->id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'geometry' => DB::raw("ST_GeomFromText('{$geometryWkt}', 4326)"),
                    'centroid' => DB::raw("ST_GeomFromText('{$centroidWkt}', 4326)"),
                ];
            } catch (\Exception $e) {
                Log::error('Error processing village: ' . ($feature['properties']['NAME_5'] ?? 'Unknown') . ' - ' . $e->getMessage());
                continue;
            }

            // Insert batch when it reaches the batch size
            if (count($villages) >= $batchSize) {
                Village::insert($villages);
                $villages = []; // Reset the batch
            }
        }

        // Insert any remaining villages
        if (!empty($villages)) {
            Village::insert($villages);
        }

        Log::info('Village seeding completed. ' . Village::count() . ' villages created on ' . now()->toDateTimeString() . ' CAT.');
    }
}

// TODO , contant minifra for mistakes in thier dataset