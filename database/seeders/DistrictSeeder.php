<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use App\Models\District;
use App\Models\Province;
use Spinen\Geometry\Geometry;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DistrictSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */

    protected $geometry;

    public function __construct(Geometry $geometry)
    {
        $this->geometry = $geometry;
    }

    public function run(): void
    {

        $geojsonPath = storage_path('app/District_Boundaries.geojson');
        if (!File::exists($geojsonPath)) {
            throw new \Exception('GeoJSON file not found at: ' . $geojsonPath);
        }

        $geojson = json_decode(File::get($geojsonPath), true);

        if (!isset($geojson['features']) || empty($geojson['features'])) {
            throw new \Exception('No features found in GeoJSON file');
        }

        foreach ($geojson['features'] as $feature) {

            try {

                if (!isset($feature['geometry']) || !in_array($feature['geometry']['type'], ['Polygon', 'MultiPolygon'])) {
                    Log::warning('Invalid or missing geometry for feature: ' . json_encode($feature['properties']));
                    continue;
                }

                $geometryObject = $this->geometry->parseGeoJson(json_encode($feature['geometry']));
                $geometryWkt = $geometryObject->toWkt();
                $centroidObject = $geometryObject->centroid();
                $longitude = $centroidObject->x();
                $latitude = $centroidObject->y();
                $centroidWkt = "POINT($longitude $latitude)";

                District::create([
                    'name' => $feature['properties']['district'] ?? null,
                    'code' => $feature['properties']['dist_code'] ?? null,
                    'capture_year' => $feature['properties']['capture'] ?? null,
                    'source' => $feature['properties']['source'] ?? null,
                    'geojson' => json_encode($feature['geometry']),
                    'shape_length' => $feature['properties']['shape_Length'] ?? null,
                    'shape_area' => $feature['properties']['shape_Area'] ?? null,
                    'population' => $feature['properties']['population'] ?? null,
                    'description' => $feature['properties']['description'] ?? null,
                    'province_id' => Province::where('code', $feature['properties']['prov_code'])->first()->id,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'geometry' => DB::raw("ST_GeomFromText('{$geometryWkt}', 4326)"),
                    'centroid' => DB::raw("ST_GeomFromText('{$centroidWkt}', 4326)"),
                ]);
            } catch (\Exception $e) {
                Log::error('Error processing feature: ' . json_encode($feature['properties']) . ' - ' . $e->getMessage());
                continue;
            }
        }

        Log::info('districts seeding completed. ' . District::count() . ' district created.');
    }
}
